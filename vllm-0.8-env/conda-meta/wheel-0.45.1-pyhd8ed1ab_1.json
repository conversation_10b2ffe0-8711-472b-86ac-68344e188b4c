{"arch": null, "build": "pyhd8ed1ab_1", "build_number": 1, "build_string": "pyhd8ed1ab_1", "channel": "conda-forge", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/wheel-0.45.1-pyhd8ed1ab_1", "files": ["lib/python3.10/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "lib/python3.10/site-packages/wheel-0.45.1.dist-info/METADATA", "lib/python3.10/site-packages/wheel-0.45.1.dist-info/RECORD", "lib/python3.10/site-packages/wheel-0.45.1.dist-info/WHEEL", "lib/python3.10/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "lib/python3.10/site-packages/wheel/__init__.py", "lib/python3.10/site-packages/wheel/__main__.py", "lib/python3.10/site-packages/wheel/_bdist_wheel.py", "lib/python3.10/site-packages/wheel/_setuptools_logging.py", "lib/python3.10/site-packages/wheel/bdist_wheel.py", "lib/python3.10/site-packages/wheel/cli/__init__.py", "lib/python3.10/site-packages/wheel/cli/convert.py", "lib/python3.10/site-packages/wheel/cli/pack.py", "lib/python3.10/site-packages/wheel/cli/tags.py", "lib/python3.10/site-packages/wheel/cli/unpack.py", "lib/python3.10/site-packages/wheel/macosx_libfile.py", "lib/python3.10/site-packages/wheel/metadata.py", "lib/python3.10/site-packages/wheel/util.py", "lib/python3.10/site-packages/wheel/vendored/__init__.py", "lib/python3.10/site-packages/wheel/vendored/packaging/LICENSE", "lib/python3.10/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "lib/python3.10/site-packages/wheel/vendored/packaging/LICENSE.BSD", "lib/python3.10/site-packages/wheel/vendored/packaging/__init__.py", "lib/python3.10/site-packages/wheel/vendored/packaging/_elffile.py", "lib/python3.10/site-packages/wheel/vendored/packaging/_manylinux.py", "lib/python3.10/site-packages/wheel/vendored/packaging/_musllinux.py", "lib/python3.10/site-packages/wheel/vendored/packaging/_parser.py", "lib/python3.10/site-packages/wheel/vendored/packaging/_structures.py", "lib/python3.10/site-packages/wheel/vendored/packaging/_tokenizer.py", "lib/python3.10/site-packages/wheel/vendored/packaging/markers.py", "lib/python3.10/site-packages/wheel/vendored/packaging/requirements.py", "lib/python3.10/site-packages/wheel/vendored/packaging/specifiers.py", "lib/python3.10/site-packages/wheel/vendored/packaging/tags.py", "lib/python3.10/site-packages/wheel/vendored/packaging/utils.py", "lib/python3.10/site-packages/wheel/vendored/packaging/version.py", "lib/python3.10/site-packages/wheel/vendored/vendor.txt", "lib/python3.10/site-packages/wheel/wheelfile.py", "lib/python3.10/site-packages/wheel/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/wheel/__pycache__/__main__.cpython-310.pyc", "lib/python3.10/site-packages/wheel/__pycache__/_bdist_wheel.cpython-310.pyc", "lib/python3.10/site-packages/wheel/__pycache__/_setuptools_logging.cpython-310.pyc", "lib/python3.10/site-packages/wheel/__pycache__/bdist_wheel.cpython-310.pyc", "lib/python3.10/site-packages/wheel/cli/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/wheel/cli/__pycache__/convert.cpython-310.pyc", "lib/python3.10/site-packages/wheel/cli/__pycache__/pack.cpython-310.pyc", "lib/python3.10/site-packages/wheel/cli/__pycache__/tags.cpython-310.pyc", "lib/python3.10/site-packages/wheel/cli/__pycache__/unpack.cpython-310.pyc", "lib/python3.10/site-packages/wheel/__pycache__/macosx_libfile.cpython-310.pyc", "lib/python3.10/site-packages/wheel/__pycache__/metadata.cpython-310.pyc", "lib/python3.10/site-packages/wheel/__pycache__/util.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-310.pyc", "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-310.pyc", "lib/python3.10/site-packages/wheel/__pycache__/wheelfile.cpython-310.pyc", "bin/wheel"], "fn": "wheel-0.45.1-pyhd8ed1ab_1.conda", "license": "MIT", "link": {"source": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/wheel-0.45.1-pyhd8ed1ab_1", "type": 1}, "md5": "75cb7132eb58d97896e173ef12ac9986", "name": "wheel", "noarch": "python", "package_tarball_full_path": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/wheel-0.45.1-pyhd8ed1ab_1.tar.bz2", "paths_data": {"paths": [{"_path": "lib/python3.10/site-packages/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "lib/python3.10/site-packages/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "sha256_in_prefix": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "size_in_bytes": 2313}, {"_path": "lib/python3.10/site-packages/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "5fe4585249829c50d2e9c0f276088c5615e9da7d4c97ff5d3573d073964a5e1d", "sha256_in_prefix": "5fe4585249829c50d2e9c0f276088c5615e9da7d4c97ff5d3573d073964a5e1d", "size_in_bytes": 3180}, {"_path": "lib/python3.10/site-packages/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "lib/python3.10/site-packages/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "lib/python3.10/site-packages/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "lib/python3.10/site-packages/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "lib/python3.10/site-packages/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "lib/python3.10/site-packages/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "lib/python3.10/site-packages/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "lib/python3.10/site-packages/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "lib/python3.10/site-packages/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "lib/python3.10/site-packages/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "lib/python3.10/site-packages/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "lib/python3.10/site-packages/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "lib/python3.10/site-packages/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "lib/python3.10/site-packages/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "lib/python3.10/site-packages/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "lib/python3.10/site-packages/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "lib/python3.10/site-packages/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "lib/python3.10/site-packages/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "lib/python3.10/site-packages/wheel/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/__pycache__/_bdist_wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/__pycache__/_setuptools_logging.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/__pycache__/bdist_wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/cli/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/cli/__pycache__/convert.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/cli/__pycache__/pack.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/cli/__pycache__/tags.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/cli/__pycache__/unpack.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/__pycache__/macosx_libfile.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/__pycache__/metadata.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/__pycache__/util.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/wheel/__pycache__/wheelfile.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "bin/wheel", "path_type": "unix_python_entry_point"}], "paths_version": 1}, "platform": null, "requested_spec": "", "sha256": "1b34021e815ff89a4d902d879c3bd2040bc1bd6169b32e9427497fa05c55f1ce", "size": 62931, "subdir": "noarch", "timestamp": 1733130309598, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/noarch/wheel-0.45.1-pyhd8ed1ab_1.conda", "version": "0.45.1"}
==> 2025-08-14 11:04:48 <==
# cmd: /share_data/users/yukaifeng/anaconda3/bin/mamba create -p ./vllm-0.8-env python=3.10
# conda version: 3.8.0
+conda-forge::ld_impl_linux-64-2.44-h1423503_1
+conda-forge::libgomp-15.1.0-h767d61c_4
+conda-forge::_libgcc_mutex-0.1-conda_forge
+conda-forge::_openmp_mutex-4.5-2_gnu
+conda-forge::libgcc-15.1.0-h767d61c_4
+conda-forge::ncurses-6.5-h2d0b736_3
+conda-forge::libzlib-1.3.1-hb9d3cd8_2
+conda-forge::libnsl-2.0.1-hb9d3cd8_1
+conda-forge::liblzma-5.8.1-hb9d3cd8_2
+conda-forge::libgcc-ng-15.1.0-h69a702a_4
+conda-forge::libffi-3.4.6-h2dba641_1
+conda-forge::libexpat-2.7.1-hecca717_0
+conda-forge::readline-8.2-h8c095d6_2
+conda-forge::libsqlite-3.50.4-h0c1763c_0
+conda-forge::tk-8.6.13-noxft_hd72426e_102
+conda-forge::libxcrypt-4.4.36-hd590300_1
+conda-forge::bzip2-1.0.8-h4bc722e_7
+conda-forge::libuuid-2.38.1-h0b41bf4_0
+conda-forge::tzdata-2025b-h78e105d_0
+conda-forge::ca-certificates-2025.8.3-hbd8a1cb_0
+conda-forge::openssl-3.5.2-h26f9b46_0
+conda-forge::python-3.10.18-hd6af730_0_cpython
+conda-forge::wheel-0.45.1-pyhd8ed1ab_1
+conda-forge::setuptools-80.9.0-pyhff2d567_0
+conda-forge::pip-25.2-pyh8b19718_0
# update specs: ["python=3.10"]

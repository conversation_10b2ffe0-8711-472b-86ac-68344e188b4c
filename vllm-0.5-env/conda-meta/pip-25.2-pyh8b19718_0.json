{"arch": null, "build": "pyh8b19718_0", "build_number": 0, "build_string": "pyh8b19718_0", "channel": "conda-forge", "constrains": [], "depends": ["python >=3.9,<3.13.0a0", "setuptools", "wheel"], "extracted_package_dir": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/pip-25.2-pyh8b19718_0", "files": ["lib/python3.10/site-packages/pip-25.2.dist-info/INSTALLER", "lib/python3.10/site-packages/pip-25.2.dist-info/METADATA", "lib/python3.10/site-packages/pip-25.2.dist-info/RECORD", "lib/python3.10/site-packages/pip-25.2.dist-info/REQUESTED", "lib/python3.10/site-packages/pip-25.2.dist-info/WHEEL", "lib/python3.10/site-packages/pip-25.2.dist-info/direct_url.json", "lib/python3.10/site-packages/pip-25.2.dist-info/entry_points.txt", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/AUTHORS.txt", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/LICENSE.txt", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/cachecontrol/LICENSE.txt", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/certifi/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/dependency_groups/LICENSE.txt", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/distlib/LICENSE.txt", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/distro/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/idna/LICENSE.md", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/msgpack/COPYING", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/packaging/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/packaging/LICENSE.APACHE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/packaging/LICENSE.BSD", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/pkg_resources/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/platformdirs/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/pygments/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/pyproject_hooks/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/requests/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/resolvelib/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/rich/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/tomli/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/tomli/LICENSE-HEADER", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/tomli_w/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/truststore/LICENSE", "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/urllib3/LICENSE.txt", "lib/python3.10/site-packages/pip-25.2.dist-info/top_level.txt", "lib/python3.10/site-packages/pip/__init__.py", "lib/python3.10/site-packages/pip/__main__.py", "lib/python3.10/site-packages/pip/__pip-runner__.py", "lib/python3.10/site-packages/pip/_internal/__init__.py", "lib/python3.10/site-packages/pip/_internal/build_env.py", "lib/python3.10/site-packages/pip/_internal/cache.py", "lib/python3.10/site-packages/pip/_internal/cli/__init__.py", "lib/python3.10/site-packages/pip/_internal/cli/autocompletion.py", "lib/python3.10/site-packages/pip/_internal/cli/base_command.py", "lib/python3.10/site-packages/pip/_internal/cli/cmdoptions.py", "lib/python3.10/site-packages/pip/_internal/cli/command_context.py", "lib/python3.10/site-packages/pip/_internal/cli/index_command.py", "lib/python3.10/site-packages/pip/_internal/cli/main.py", "lib/python3.10/site-packages/pip/_internal/cli/main_parser.py", "lib/python3.10/site-packages/pip/_internal/cli/parser.py", "lib/python3.10/site-packages/pip/_internal/cli/progress_bars.py", "lib/python3.10/site-packages/pip/_internal/cli/req_command.py", "lib/python3.10/site-packages/pip/_internal/cli/spinners.py", "lib/python3.10/site-packages/pip/_internal/cli/status_codes.py", "lib/python3.10/site-packages/pip/_internal/commands/__init__.py", "lib/python3.10/site-packages/pip/_internal/commands/cache.py", "lib/python3.10/site-packages/pip/_internal/commands/check.py", "lib/python3.10/site-packages/pip/_internal/commands/completion.py", "lib/python3.10/site-packages/pip/_internal/commands/configuration.py", "lib/python3.10/site-packages/pip/_internal/commands/debug.py", "lib/python3.10/site-packages/pip/_internal/commands/download.py", "lib/python3.10/site-packages/pip/_internal/commands/freeze.py", "lib/python3.10/site-packages/pip/_internal/commands/hash.py", "lib/python3.10/site-packages/pip/_internal/commands/help.py", "lib/python3.10/site-packages/pip/_internal/commands/index.py", "lib/python3.10/site-packages/pip/_internal/commands/inspect.py", "lib/python3.10/site-packages/pip/_internal/commands/install.py", "lib/python3.10/site-packages/pip/_internal/commands/list.py", "lib/python3.10/site-packages/pip/_internal/commands/lock.py", "lib/python3.10/site-packages/pip/_internal/commands/search.py", "lib/python3.10/site-packages/pip/_internal/commands/show.py", "lib/python3.10/site-packages/pip/_internal/commands/uninstall.py", "lib/python3.10/site-packages/pip/_internal/commands/wheel.py", "lib/python3.10/site-packages/pip/_internal/configuration.py", "lib/python3.10/site-packages/pip/_internal/distributions/__init__.py", "lib/python3.10/site-packages/pip/_internal/distributions/base.py", "lib/python3.10/site-packages/pip/_internal/distributions/installed.py", "lib/python3.10/site-packages/pip/_internal/distributions/sdist.py", "lib/python3.10/site-packages/pip/_internal/distributions/wheel.py", "lib/python3.10/site-packages/pip/_internal/exceptions.py", "lib/python3.10/site-packages/pip/_internal/index/__init__.py", "lib/python3.10/site-packages/pip/_internal/index/collector.py", "lib/python3.10/site-packages/pip/_internal/index/package_finder.py", "lib/python3.10/site-packages/pip/_internal/index/sources.py", "lib/python3.10/site-packages/pip/_internal/locations/__init__.py", "lib/python3.10/site-packages/pip/_internal/locations/_distutils.py", "lib/python3.10/site-packages/pip/_internal/locations/_sysconfig.py", "lib/python3.10/site-packages/pip/_internal/locations/base.py", "lib/python3.10/site-packages/pip/_internal/main.py", "lib/python3.10/site-packages/pip/_internal/metadata/__init__.py", "lib/python3.10/site-packages/pip/_internal/metadata/_json.py", "lib/python3.10/site-packages/pip/_internal/metadata/base.py", "lib/python3.10/site-packages/pip/_internal/metadata/importlib/__init__.py", "lib/python3.10/site-packages/pip/_internal/metadata/importlib/_compat.py", "lib/python3.10/site-packages/pip/_internal/metadata/importlib/_dists.py", "lib/python3.10/site-packages/pip/_internal/metadata/importlib/_envs.py", "lib/python3.10/site-packages/pip/_internal/metadata/pkg_resources.py", "lib/python3.10/site-packages/pip/_internal/models/__init__.py", "lib/python3.10/site-packages/pip/_internal/models/candidate.py", "lib/python3.10/site-packages/pip/_internal/models/direct_url.py", "lib/python3.10/site-packages/pip/_internal/models/format_control.py", "lib/python3.10/site-packages/pip/_internal/models/index.py", "lib/python3.10/site-packages/pip/_internal/models/installation_report.py", "lib/python3.10/site-packages/pip/_internal/models/link.py", "lib/python3.10/site-packages/pip/_internal/models/pylock.py", "lib/python3.10/site-packages/pip/_internal/models/scheme.py", "lib/python3.10/site-packages/pip/_internal/models/search_scope.py", "lib/python3.10/site-packages/pip/_internal/models/selection_prefs.py", "lib/python3.10/site-packages/pip/_internal/models/target_python.py", "lib/python3.10/site-packages/pip/_internal/models/wheel.py", "lib/python3.10/site-packages/pip/_internal/network/__init__.py", "lib/python3.10/site-packages/pip/_internal/network/auth.py", "lib/python3.10/site-packages/pip/_internal/network/cache.py", "lib/python3.10/site-packages/pip/_internal/network/download.py", "lib/python3.10/site-packages/pip/_internal/network/lazy_wheel.py", "lib/python3.10/site-packages/pip/_internal/network/session.py", "lib/python3.10/site-packages/pip/_internal/network/utils.py", "lib/python3.10/site-packages/pip/_internal/network/xmlrpc.py", "lib/python3.10/site-packages/pip/_internal/operations/__init__.py", "lib/python3.10/site-packages/pip/_internal/operations/build/__init__.py", "lib/python3.10/site-packages/pip/_internal/operations/build/build_tracker.py", "lib/python3.10/site-packages/pip/_internal/operations/build/metadata.py", "lib/python3.10/site-packages/pip/_internal/operations/build/metadata_editable.py", "lib/python3.10/site-packages/pip/_internal/operations/build/metadata_legacy.py", "lib/python3.10/site-packages/pip/_internal/operations/build/wheel.py", "lib/python3.10/site-packages/pip/_internal/operations/build/wheel_editable.py", "lib/python3.10/site-packages/pip/_internal/operations/build/wheel_legacy.py", "lib/python3.10/site-packages/pip/_internal/operations/check.py", "lib/python3.10/site-packages/pip/_internal/operations/freeze.py", "lib/python3.10/site-packages/pip/_internal/operations/install/__init__.py", "lib/python3.10/site-packages/pip/_internal/operations/install/editable_legacy.py", "lib/python3.10/site-packages/pip/_internal/operations/install/wheel.py", "lib/python3.10/site-packages/pip/_internal/operations/prepare.py", "lib/python3.10/site-packages/pip/_internal/pyproject.py", "lib/python3.10/site-packages/pip/_internal/req/__init__.py", "lib/python3.10/site-packages/pip/_internal/req/constructors.py", "lib/python3.10/site-packages/pip/_internal/req/req_dependency_group.py", "lib/python3.10/site-packages/pip/_internal/req/req_file.py", "lib/python3.10/site-packages/pip/_internal/req/req_install.py", "lib/python3.10/site-packages/pip/_internal/req/req_set.py", "lib/python3.10/site-packages/pip/_internal/req/req_uninstall.py", "lib/python3.10/site-packages/pip/_internal/resolution/__init__.py", "lib/python3.10/site-packages/pip/_internal/resolution/base.py", "lib/python3.10/site-packages/pip/_internal/resolution/legacy/__init__.py", "lib/python3.10/site-packages/pip/_internal/resolution/legacy/resolver.py", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__init__.py", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/base.py", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/candidates.py", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/factory.py", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/found_candidates.py", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/provider.py", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/reporter.py", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/requirements.py", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/resolver.py", "lib/python3.10/site-packages/pip/_internal/self_outdated_check.py", "lib/python3.10/site-packages/pip/_internal/utils/__init__.py", "lib/python3.10/site-packages/pip/_internal/utils/_jaraco_text.py", "lib/python3.10/site-packages/pip/_internal/utils/_log.py", "lib/python3.10/site-packages/pip/_internal/utils/appdirs.py", "lib/python3.10/site-packages/pip/_internal/utils/compat.py", "lib/python3.10/site-packages/pip/_internal/utils/compatibility_tags.py", "lib/python3.10/site-packages/pip/_internal/utils/datetime.py", "lib/python3.10/site-packages/pip/_internal/utils/deprecation.py", "lib/python3.10/site-packages/pip/_internal/utils/direct_url_helpers.py", "lib/python3.10/site-packages/pip/_internal/utils/egg_link.py", "lib/python3.10/site-packages/pip/_internal/utils/entrypoints.py", "lib/python3.10/site-packages/pip/_internal/utils/filesystem.py", "lib/python3.10/site-packages/pip/_internal/utils/filetypes.py", "lib/python3.10/site-packages/pip/_internal/utils/glibc.py", "lib/python3.10/site-packages/pip/_internal/utils/hashes.py", "lib/python3.10/site-packages/pip/_internal/utils/logging.py", "lib/python3.10/site-packages/pip/_internal/utils/misc.py", "lib/python3.10/site-packages/pip/_internal/utils/packaging.py", "lib/python3.10/site-packages/pip/_internal/utils/retry.py", "lib/python3.10/site-packages/pip/_internal/utils/setuptools_build.py", "lib/python3.10/site-packages/pip/_internal/utils/subprocess.py", "lib/python3.10/site-packages/pip/_internal/utils/temp_dir.py", "lib/python3.10/site-packages/pip/_internal/utils/unpacking.py", "lib/python3.10/site-packages/pip/_internal/utils/urls.py", "lib/python3.10/site-packages/pip/_internal/utils/virtualenv.py", "lib/python3.10/site-packages/pip/_internal/utils/wheel.py", "lib/python3.10/site-packages/pip/_internal/vcs/__init__.py", "lib/python3.10/site-packages/pip/_internal/vcs/bazaar.py", "lib/python3.10/site-packages/pip/_internal/vcs/git.py", "lib/python3.10/site-packages/pip/_internal/vcs/mercurial.py", "lib/python3.10/site-packages/pip/_internal/vcs/subversion.py", "lib/python3.10/site-packages/pip/_internal/vcs/versioncontrol.py", "lib/python3.10/site-packages/pip/_internal/wheel_builder.py", "lib/python3.10/site-packages/pip/_vendor/__init__.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__init__.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/_cmd.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/adapter.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/cache.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/__init__.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/file_cache.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/redis_cache.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/controller.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/filewrapper.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/heuristics.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/py.typed", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/serialize.py", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/wrapper.py", "lib/python3.10/site-packages/pip/_vendor/certifi/__init__.py", "lib/python3.10/site-packages/pip/_vendor/certifi/__main__.py", "lib/python3.10/site-packages/pip/_vendor/certifi/cacert.pem", "lib/python3.10/site-packages/pip/_vendor/certifi/core.py", "lib/python3.10/site-packages/pip/_vendor/certifi/py.typed", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__init__.py", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__main__.py", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/_implementation.py", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/_lint_dependency_groups.py", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/_pip_wrapper.py", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/_toml_compat.py", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/py.typed", "lib/python3.10/site-packages/pip/_vendor/distlib/__init__.py", "lib/python3.10/site-packages/pip/_vendor/distlib/compat.py", "lib/python3.10/site-packages/pip/_vendor/distlib/resources.py", "lib/python3.10/site-packages/pip/_vendor/distlib/scripts.py", "lib/python3.10/site-packages/pip/_vendor/distlib/t32.exe", "lib/python3.10/site-packages/pip/_vendor/distlib/t64-arm.exe", "lib/python3.10/site-packages/pip/_vendor/distlib/t64.exe", "lib/python3.10/site-packages/pip/_vendor/distlib/util.py", "lib/python3.10/site-packages/pip/_vendor/distlib/w32.exe", "lib/python3.10/site-packages/pip/_vendor/distlib/w64-arm.exe", "lib/python3.10/site-packages/pip/_vendor/distlib/w64.exe", "lib/python3.10/site-packages/pip/_vendor/distro/__init__.py", "lib/python3.10/site-packages/pip/_vendor/distro/__main__.py", "lib/python3.10/site-packages/pip/_vendor/distro/distro.py", "lib/python3.10/site-packages/pip/_vendor/distro/py.typed", "lib/python3.10/site-packages/pip/_vendor/idna/__init__.py", "lib/python3.10/site-packages/pip/_vendor/idna/codec.py", "lib/python3.10/site-packages/pip/_vendor/idna/compat.py", "lib/python3.10/site-packages/pip/_vendor/idna/core.py", "lib/python3.10/site-packages/pip/_vendor/idna/idnadata.py", "lib/python3.10/site-packages/pip/_vendor/idna/intranges.py", "lib/python3.10/site-packages/pip/_vendor/idna/package_data.py", "lib/python3.10/site-packages/pip/_vendor/idna/py.typed", "lib/python3.10/site-packages/pip/_vendor/idna/uts46data.py", "lib/python3.10/site-packages/pip/_vendor/msgpack/__init__.py", "lib/python3.10/site-packages/pip/_vendor/msgpack/exceptions.py", "lib/python3.10/site-packages/pip/_vendor/msgpack/ext.py", "lib/python3.10/site-packages/pip/_vendor/msgpack/fallback.py", "lib/python3.10/site-packages/pip/_vendor/packaging/__init__.py", "lib/python3.10/site-packages/pip/_vendor/packaging/_elffile.py", "lib/python3.10/site-packages/pip/_vendor/packaging/_manylinux.py", "lib/python3.10/site-packages/pip/_vendor/packaging/_musllinux.py", "lib/python3.10/site-packages/pip/_vendor/packaging/_parser.py", "lib/python3.10/site-packages/pip/_vendor/packaging/_structures.py", "lib/python3.10/site-packages/pip/_vendor/packaging/_tokenizer.py", "lib/python3.10/site-packages/pip/_vendor/packaging/licenses/__init__.py", "lib/python3.10/site-packages/pip/_vendor/packaging/licenses/_spdx.py", "lib/python3.10/site-packages/pip/_vendor/packaging/markers.py", "lib/python3.10/site-packages/pip/_vendor/packaging/metadata.py", "lib/python3.10/site-packages/pip/_vendor/packaging/py.typed", "lib/python3.10/site-packages/pip/_vendor/packaging/requirements.py", "lib/python3.10/site-packages/pip/_vendor/packaging/specifiers.py", "lib/python3.10/site-packages/pip/_vendor/packaging/tags.py", "lib/python3.10/site-packages/pip/_vendor/packaging/utils.py", "lib/python3.10/site-packages/pip/_vendor/packaging/version.py", "lib/python3.10/site-packages/pip/_vendor/pkg_resources/__init__.py", "lib/python3.10/site-packages/pip/_vendor/platformdirs/__init__.py", "lib/python3.10/site-packages/pip/_vendor/platformdirs/__main__.py", "lib/python3.10/site-packages/pip/_vendor/platformdirs/android.py", "lib/python3.10/site-packages/pip/_vendor/platformdirs/api.py", "lib/python3.10/site-packages/pip/_vendor/platformdirs/macos.py", "lib/python3.10/site-packages/pip/_vendor/platformdirs/py.typed", "lib/python3.10/site-packages/pip/_vendor/platformdirs/unix.py", "lib/python3.10/site-packages/pip/_vendor/platformdirs/version.py", "lib/python3.10/site-packages/pip/_vendor/platformdirs/windows.py", "lib/python3.10/site-packages/pip/_vendor/pygments/__init__.py", "lib/python3.10/site-packages/pip/_vendor/pygments/__main__.py", "lib/python3.10/site-packages/pip/_vendor/pygments/console.py", "lib/python3.10/site-packages/pip/_vendor/pygments/filter.py", "lib/python3.10/site-packages/pip/_vendor/pygments/filters/__init__.py", "lib/python3.10/site-packages/pip/_vendor/pygments/formatter.py", "lib/python3.10/site-packages/pip/_vendor/pygments/formatters/__init__.py", "lib/python3.10/site-packages/pip/_vendor/pygments/formatters/_mapping.py", "lib/python3.10/site-packages/pip/_vendor/pygments/lexer.py", "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/__init__.py", "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/_mapping.py", "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/python.py", "lib/python3.10/site-packages/pip/_vendor/pygments/modeline.py", "lib/python3.10/site-packages/pip/_vendor/pygments/plugin.py", "lib/python3.10/site-packages/pip/_vendor/pygments/regexopt.py", "lib/python3.10/site-packages/pip/_vendor/pygments/scanner.py", "lib/python3.10/site-packages/pip/_vendor/pygments/sphinxext.py", "lib/python3.10/site-packages/pip/_vendor/pygments/style.py", "lib/python3.10/site-packages/pip/_vendor/pygments/styles/__init__.py", "lib/python3.10/site-packages/pip/_vendor/pygments/styles/_mapping.py", "lib/python3.10/site-packages/pip/_vendor/pygments/token.py", "lib/python3.10/site-packages/pip/_vendor/pygments/unistring.py", "lib/python3.10/site-packages/pip/_vendor/pygments/util.py", "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/__init__.py", "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/_impl.py", "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/_in_process/__init__.py", "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/_in_process/_in_process.py", "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/py.typed", "lib/python3.10/site-packages/pip/_vendor/requests/__init__.py", "lib/python3.10/site-packages/pip/_vendor/requests/__version__.py", "lib/python3.10/site-packages/pip/_vendor/requests/_internal_utils.py", "lib/python3.10/site-packages/pip/_vendor/requests/adapters.py", "lib/python3.10/site-packages/pip/_vendor/requests/api.py", "lib/python3.10/site-packages/pip/_vendor/requests/auth.py", "lib/python3.10/site-packages/pip/_vendor/requests/certs.py", "lib/python3.10/site-packages/pip/_vendor/requests/compat.py", "lib/python3.10/site-packages/pip/_vendor/requests/cookies.py", "lib/python3.10/site-packages/pip/_vendor/requests/exceptions.py", "lib/python3.10/site-packages/pip/_vendor/requests/help.py", "lib/python3.10/site-packages/pip/_vendor/requests/hooks.py", "lib/python3.10/site-packages/pip/_vendor/requests/models.py", "lib/python3.10/site-packages/pip/_vendor/requests/packages.py", "lib/python3.10/site-packages/pip/_vendor/requests/sessions.py", "lib/python3.10/site-packages/pip/_vendor/requests/status_codes.py", "lib/python3.10/site-packages/pip/_vendor/requests/structures.py", "lib/python3.10/site-packages/pip/_vendor/requests/utils.py", "lib/python3.10/site-packages/pip/_vendor/resolvelib/__init__.py", "lib/python3.10/site-packages/pip/_vendor/resolvelib/providers.py", "lib/python3.10/site-packages/pip/_vendor/resolvelib/py.typed", "lib/python3.10/site-packages/pip/_vendor/resolvelib/reporters.py", "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__init__.py", "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/abstract.py", "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/criterion.py", "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/exceptions.py", "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/resolution.py", "lib/python3.10/site-packages/pip/_vendor/resolvelib/structs.py", "lib/python3.10/site-packages/pip/_vendor/rich/__init__.py", "lib/python3.10/site-packages/pip/_vendor/rich/__main__.py", "lib/python3.10/site-packages/pip/_vendor/rich/_cell_widths.py", "lib/python3.10/site-packages/pip/_vendor/rich/_emoji_codes.py", "lib/python3.10/site-packages/pip/_vendor/rich/_emoji_replace.py", "lib/python3.10/site-packages/pip/_vendor/rich/_export_format.py", "lib/python3.10/site-packages/pip/_vendor/rich/_extension.py", "lib/python3.10/site-packages/pip/_vendor/rich/_fileno.py", "lib/python3.10/site-packages/pip/_vendor/rich/_inspect.py", "lib/python3.10/site-packages/pip/_vendor/rich/_log_render.py", "lib/python3.10/site-packages/pip/_vendor/rich/_loop.py", "lib/python3.10/site-packages/pip/_vendor/rich/_null_file.py", "lib/python3.10/site-packages/pip/_vendor/rich/_palettes.py", "lib/python3.10/site-packages/pip/_vendor/rich/_pick.py", "lib/python3.10/site-packages/pip/_vendor/rich/_ratio.py", "lib/python3.10/site-packages/pip/_vendor/rich/_spinners.py", "lib/python3.10/site-packages/pip/_vendor/rich/_stack.py", "lib/python3.10/site-packages/pip/_vendor/rich/_timer.py", "lib/python3.10/site-packages/pip/_vendor/rich/_win32_console.py", "lib/python3.10/site-packages/pip/_vendor/rich/_windows.py", "lib/python3.10/site-packages/pip/_vendor/rich/_windows_renderer.py", "lib/python3.10/site-packages/pip/_vendor/rich/_wrap.py", "lib/python3.10/site-packages/pip/_vendor/rich/abc.py", "lib/python3.10/site-packages/pip/_vendor/rich/align.py", "lib/python3.10/site-packages/pip/_vendor/rich/ansi.py", "lib/python3.10/site-packages/pip/_vendor/rich/bar.py", "lib/python3.10/site-packages/pip/_vendor/rich/box.py", "lib/python3.10/site-packages/pip/_vendor/rich/cells.py", "lib/python3.10/site-packages/pip/_vendor/rich/color.py", "lib/python3.10/site-packages/pip/_vendor/rich/color_triplet.py", "lib/python3.10/site-packages/pip/_vendor/rich/columns.py", "lib/python3.10/site-packages/pip/_vendor/rich/console.py", "lib/python3.10/site-packages/pip/_vendor/rich/constrain.py", "lib/python3.10/site-packages/pip/_vendor/rich/containers.py", "lib/python3.10/site-packages/pip/_vendor/rich/control.py", "lib/python3.10/site-packages/pip/_vendor/rich/default_styles.py", "lib/python3.10/site-packages/pip/_vendor/rich/diagnose.py", "lib/python3.10/site-packages/pip/_vendor/rich/emoji.py", "lib/python3.10/site-packages/pip/_vendor/rich/errors.py", "lib/python3.10/site-packages/pip/_vendor/rich/file_proxy.py", "lib/python3.10/site-packages/pip/_vendor/rich/filesize.py", "lib/python3.10/site-packages/pip/_vendor/rich/highlighter.py", "lib/python3.10/site-packages/pip/_vendor/rich/json.py", "lib/python3.10/site-packages/pip/_vendor/rich/jupyter.py", "lib/python3.10/site-packages/pip/_vendor/rich/layout.py", "lib/python3.10/site-packages/pip/_vendor/rich/live.py", "lib/python3.10/site-packages/pip/_vendor/rich/live_render.py", "lib/python3.10/site-packages/pip/_vendor/rich/logging.py", "lib/python3.10/site-packages/pip/_vendor/rich/markup.py", "lib/python3.10/site-packages/pip/_vendor/rich/measure.py", "lib/python3.10/site-packages/pip/_vendor/rich/padding.py", "lib/python3.10/site-packages/pip/_vendor/rich/pager.py", "lib/python3.10/site-packages/pip/_vendor/rich/palette.py", "lib/python3.10/site-packages/pip/_vendor/rich/panel.py", "lib/python3.10/site-packages/pip/_vendor/rich/pretty.py", "lib/python3.10/site-packages/pip/_vendor/rich/progress.py", "lib/python3.10/site-packages/pip/_vendor/rich/progress_bar.py", "lib/python3.10/site-packages/pip/_vendor/rich/prompt.py", "lib/python3.10/site-packages/pip/_vendor/rich/protocol.py", "lib/python3.10/site-packages/pip/_vendor/rich/py.typed", "lib/python3.10/site-packages/pip/_vendor/rich/region.py", "lib/python3.10/site-packages/pip/_vendor/rich/repr.py", "lib/python3.10/site-packages/pip/_vendor/rich/rule.py", "lib/python3.10/site-packages/pip/_vendor/rich/scope.py", "lib/python3.10/site-packages/pip/_vendor/rich/screen.py", "lib/python3.10/site-packages/pip/_vendor/rich/segment.py", "lib/python3.10/site-packages/pip/_vendor/rich/spinner.py", "lib/python3.10/site-packages/pip/_vendor/rich/status.py", "lib/python3.10/site-packages/pip/_vendor/rich/style.py", "lib/python3.10/site-packages/pip/_vendor/rich/styled.py", "lib/python3.10/site-packages/pip/_vendor/rich/syntax.py", "lib/python3.10/site-packages/pip/_vendor/rich/table.py", "lib/python3.10/site-packages/pip/_vendor/rich/terminal_theme.py", "lib/python3.10/site-packages/pip/_vendor/rich/text.py", "lib/python3.10/site-packages/pip/_vendor/rich/theme.py", "lib/python3.10/site-packages/pip/_vendor/rich/themes.py", "lib/python3.10/site-packages/pip/_vendor/rich/traceback.py", "lib/python3.10/site-packages/pip/_vendor/rich/tree.py", "lib/python3.10/site-packages/pip/_vendor/tomli/__init__.py", "lib/python3.10/site-packages/pip/_vendor/tomli/_parser.py", "lib/python3.10/site-packages/pip/_vendor/tomli/_re.py", "lib/python3.10/site-packages/pip/_vendor/tomli/_types.py", "lib/python3.10/site-packages/pip/_vendor/tomli/py.typed", "lib/python3.10/site-packages/pip/_vendor/tomli_w/__init__.py", "lib/python3.10/site-packages/pip/_vendor/tomli_w/_writer.py", "lib/python3.10/site-packages/pip/_vendor/tomli_w/py.typed", "lib/python3.10/site-packages/pip/_vendor/truststore/__init__.py", "lib/python3.10/site-packages/pip/_vendor/truststore/_api.py", "lib/python3.10/site-packages/pip/_vendor/truststore/_macos.py", "lib/python3.10/site-packages/pip/_vendor/truststore/_openssl.py", "lib/python3.10/site-packages/pip/_vendor/truststore/_ssl_constants.py", "lib/python3.10/site-packages/pip/_vendor/truststore/_windows.py", "lib/python3.10/site-packages/pip/_vendor/truststore/py.typed", "lib/python3.10/site-packages/pip/_vendor/urllib3/__init__.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/_collections.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/_version.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/connection.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/connectionpool.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__init__.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_appengine_environ.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__init__.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/bindings.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/low_level.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/appengine.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/ntlmpool.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/pyopenssl.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/securetransport.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/socks.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/exceptions.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/fields.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/filepost.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/__init__.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/__init__.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/makefile.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/weakref_finalize.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/six.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/poolmanager.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/request.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/response.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__init__.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/connection.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/proxy.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/queue.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/request.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/response.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/retry.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/ssl_.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/ssl_match_hostname.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/ssltransport.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/timeout.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/url.py", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/wait.py", "lib/python3.10/site-packages/pip/_vendor/vendor.txt", "lib/python3.10/site-packages/pip/py.typed", "lib/python3.10/site-packages/pip/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/__pycache__/__main__.cpython-310.pyc", "lib/python3.10/site-packages/pip/__pycache__/__pip-runner__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/__pycache__/build_env.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/__pycache__/cache.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/autocompletion.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/base_command.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/cmdoptions.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/command_context.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/index_command.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/main.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/main_parser.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/parser.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/progress_bars.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/req_command.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/spinners.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/status_codes.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/cache.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/check.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/completion.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/configuration.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/debug.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/download.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/freeze.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/hash.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/help.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/index.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/inspect.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/install.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/list.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/lock.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/search.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/show.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/uninstall.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/wheel.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/__pycache__/configuration.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/distributions/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/distributions/__pycache__/base.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/distributions/__pycache__/installed.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/distributions/__pycache__/sdist.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/distributions/__pycache__/wheel.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/__pycache__/exceptions.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/index/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/index/__pycache__/collector.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/index/__pycache__/package_finder.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/index/__pycache__/sources.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/locations/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/locations/__pycache__/_distutils.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/locations/__pycache__/_sysconfig.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/locations/__pycache__/base.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/__pycache__/main.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/metadata/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/metadata/__pycache__/_json.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/metadata/__pycache__/base.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/metadata/importlib/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/metadata/importlib/__pycache__/_compat.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/metadata/importlib/__pycache__/_dists.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/metadata/importlib/__pycache__/_envs.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/metadata/__pycache__/pkg_resources.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/candidate.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/direct_url.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/format_control.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/index.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/installation_report.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/link.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/pylock.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/scheme.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/search_scope.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/selection_prefs.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/target_python.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/models/__pycache__/wheel.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/network/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/network/__pycache__/auth.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/network/__pycache__/cache.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/network/__pycache__/download.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/network/__pycache__/lazy_wheel.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/network/__pycache__/session.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/network/__pycache__/utils.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/network/__pycache__/xmlrpc.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/build_tracker.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/metadata.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/metadata_editable.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/metadata_legacy.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/wheel.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/wheel_editable.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/wheel_legacy.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/__pycache__/check.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/__pycache__/freeze.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/install/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/install/__pycache__/editable_legacy.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/install/__pycache__/wheel.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/operations/__pycache__/prepare.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/__pycache__/pyproject.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/req/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/req/__pycache__/constructors.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/req/__pycache__/req_dependency_group.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/req/__pycache__/req_file.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/req/__pycache__/req_install.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/req/__pycache__/req_set.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/req/__pycache__/req_uninstall.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/__pycache__/base.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/legacy/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/legacy/__pycache__/resolver.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/base.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/candidates.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/factory.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/found_candidates.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/provider.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/reporter.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/requirements.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/resolver.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/__pycache__/self_outdated_check.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/_jaraco_text.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/_log.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/appdirs.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/compat.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/compatibility_tags.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/datetime.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/deprecation.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/direct_url_helpers.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/egg_link.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/entrypoints.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/filesystem.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/filetypes.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/glibc.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/hashes.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/logging.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/misc.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/packaging.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/retry.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/setuptools_build.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/subprocess.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/temp_dir.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/unpacking.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/urls.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/virtualenv.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/wheel.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/bazaar.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/git.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/mercurial.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/subversion.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/versioncontrol.cpython-310.pyc", "lib/python3.10/site-packages/pip/_internal/__pycache__/wheel_builder.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/_cmd.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/adapter.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/cache.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/file_cache.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/redis_cache.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/controller.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/filewrapper.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/heuristics.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/serialize.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/wrapper.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/certifi/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/certifi/__pycache__/__main__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/certifi/__pycache__/core.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/__main__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/_implementation.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/_lint_dependency_groups.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/_pip_wrapper.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/_toml_compat.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/distlib/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/distlib/__pycache__/compat.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/distlib/__pycache__/resources.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/distlib/__pycache__/scripts.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/distlib/__pycache__/util.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/distro/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/distro/__pycache__/__main__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/distro/__pycache__/distro.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/codec.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/compat.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/core.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/idnadata.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/intranges.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/package_data.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/uts46data.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/msgpack/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/msgpack/__pycache__/exceptions.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/msgpack/__pycache__/ext.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/msgpack/__pycache__/fallback.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_elffile.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_manylinux.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_musllinux.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_parser.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_structures.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_tokenizer.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/licenses/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/licenses/__pycache__/_spdx.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/markers.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/metadata.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/requirements.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/specifiers.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/tags.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/utils.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/version.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pkg_resources/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/__main__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/android.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/api.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/macos.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/unix.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/version.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/windows.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/__main__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/console.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/filter.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/filters/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/formatter.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/formatters/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/formatters/__pycache__/_mapping.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/lexer.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/__pycache__/_mapping.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/__pycache__/python.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/modeline.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/plugin.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/regexopt.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/scanner.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/sphinxext.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/style.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/styles/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/styles/__pycache__/_mapping.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/token.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/unistring.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/util.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/__pycache__/_impl.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/_in_process/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/_in_process/__pycache__/_in_process.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/__version__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/_internal_utils.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/adapters.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/api.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/auth.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/certs.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/compat.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/cookies.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/exceptions.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/help.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/hooks.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/models.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/packages.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/sessions.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/status_codes.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/structures.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/utils.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/resolvelib/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/resolvelib/__pycache__/providers.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/resolvelib/__pycache__/reporters.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/abstract.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/criterion.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/exceptions.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/resolution.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/resolvelib/__pycache__/structs.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/__main__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_cell_widths.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_emoji_codes.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_emoji_replace.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_export_format.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_extension.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_fileno.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_inspect.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_log_render.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_loop.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_null_file.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_palettes.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_pick.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_ratio.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_spinners.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_stack.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_timer.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_win32_console.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_windows.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_windows_renderer.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_wrap.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/abc.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/align.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/ansi.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/bar.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/box.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/cells.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/color.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/color_triplet.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/columns.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/console.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/constrain.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/containers.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/control.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/default_styles.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/diagnose.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/emoji.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/errors.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/file_proxy.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/filesize.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/highlighter.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/json.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/jupyter.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/layout.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/live.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/live_render.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/logging.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/markup.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/measure.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/padding.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/pager.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/palette.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/panel.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/pretty.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/progress.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/progress_bar.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/prompt.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/protocol.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/region.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/repr.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/rule.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/scope.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/screen.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/segment.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/spinner.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/status.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/style.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/styled.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/syntax.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/table.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/terminal_theme.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/text.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/theme.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/themes.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/traceback.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/tree.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/tomli/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/tomli/__pycache__/_parser.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/tomli/__pycache__/_re.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/tomli/__pycache__/_types.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/tomli_w/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/tomli_w/__pycache__/_writer.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/_api.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/_macos.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/_openssl.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/_ssl_constants.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/_windows.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/_collections.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/_version.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/connection.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/connectionpool.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/_appengine_environ.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/bindings.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/low_level.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/appengine.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/ntlmpool.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/pyopenssl.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/securetransport.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/socks.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/exceptions.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/fields.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/filepost.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/makefile.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/weakref_finalize.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/__pycache__/six.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/poolmanager.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/request.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/response.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/__init__.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/connection.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/proxy.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/queue.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/request.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/response.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/retry.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/ssl_.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/ssl_match_hostname.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/ssltransport.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/timeout.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/url.cpython-310.pyc", "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/wait.cpython-310.pyc", "bin/pip", "bin/pip3"], "fn": "pip-25.2-pyh8b19718_0.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/pip-25.2-pyh8b19718_0", "type": 1}, "md5": "dfce4b2af4bfe90cdcaf56ca0b28ddf5", "name": "pip", "noarch": "python", "package_tarball_full_path": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/pip-25.2-pyh8b19718_0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "97a3ad14d71fd8914a38f24af5b31a1f9faeb05aea66a4af40d979d6d7ad229f", "sha256_in_prefix": "97a3ad14d71fd8914a38f24af5b31a1f9faeb05aea66a4af40d979d6d7ad229f", "size_in_bytes": 4744}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "72032f0b6a9ccd12ee0e0c4227ec8fbba52afc0ad8a5e46cda6c8fa228b450ad", "sha256_in_prefix": "72032f0b6a9ccd12ee0e0c4227ec8fbba52afc0ad8a5e46cda6c8fa228b450ad", "size_in_bytes": 67333}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ff309ddcdd65ebd02bc724dbf2bcc4a0ff53a5b61792a44548d383e4ebb19d3b", "sha256_in_prefix": "ff309ddcdd65ebd02bc724dbf2bcc4a0ff53a5b61792a44548d383e4ebb19d3b", "size_in_bytes": 91}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "7d3eb44dd5a90785dbc6a1d61fc815f7cb23b19625e15c21c9401bcc73bc87fc", "sha256_in_prefix": "7d3eb44dd5a90785dbc6a1d61fc815f7cb23b19625e15c21c9401bcc73bc87fc", "size_in_bytes": 99}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "79e223bb37e77d1d8fae16e39dbcc553a327492ef49192f1c1a1c7aba33e6c3d", "sha256_in_prefix": "79e223bb37e77d1d8fae16e39dbcc553a327492ef49192f1c1a1c7aba33e6c3d", "size_in_bytes": 87}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/AUTHORS.txt", "path_type": "hardlink", "sha256": "8a811f3069248b37137083ddbe387e918ca63b513d23d76fcc78cb525292e66f", "sha256_in_prefix": "8a811f3069248b37137083ddbe387e918ca63b513d23d76fcc78cb525292e66f", "size_in_bytes": 11385}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/LICENSE.txt", "path_type": "hardlink", "sha256": "634300a669d49aeae65b12c6c48c924c51a4cdf3d1ff086dc3456dc8bcaa2104", "sha256_in_prefix": "634300a669d49aeae65b12c6c48c924c51a4cdf3d1ff086dc3456dc8bcaa2104", "size_in_bytes": 1093}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/cachecontrol/LICENSE.txt", "path_type": "hardlink", "sha256": "86eeee87be2a43f3ff1f56496f451f69243926f025fedbb033666c304c4c161b", "sha256_in_prefix": "86eeee87be2a43f3ff1f56496f451f69243926f025fedbb033666c304c4c161b", "size_in_bytes": 558}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/certifi/LICENSE", "path_type": "hardlink", "sha256": "e93716da6b9c0d5a4a1df60fe695b370f0695603d21f6f83f053e42cfc10caf7", "sha256_in_prefix": "e93716da6b9c0d5a4a1df60fe695b370f0695603d21f6f83f053e42cfc10caf7", "size_in_bytes": 989}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/dependency_groups/LICENSE.txt", "path_type": "hardlink", "sha256": "1ab36e3e2a4ba8631625384f87e9e091db1faed034c5b23325b3239abf2cc525", "sha256_in_prefix": "1ab36e3e2a4ba8631625384f87e9e091db1faed034c5b23325b3239abf2cc525", "size_in_bytes": 1099}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/distlib/LICENSE.txt", "path_type": "hardlink", "sha256": "808e10c8a6ab8deb149ff9b3fb19f447a808094606d712a9ca57fead3552599d", "sha256_in_prefix": "808e10c8a6ab8deb149ff9b3fb19f447a808094606d712a9ca57fead3552599d", "size_in_bytes": 14531}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/distro/LICENSE", "path_type": "hardlink", "sha256": "cb5e8e7e5f4a3988e1063c142c60dc2df75605f4c46515e776e3aca6df976e14", "sha256_in_prefix": "cb5e8e7e5f4a3988e1063c142c60dc2df75605f4c46515e776e3aca6df976e14", "size_in_bytes": 11325}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/idna/LICENSE.md", "path_type": "hardlink", "sha256": "a59f0b0ef3635874109a4461ca44ff7a70d50696e814767bfaf721d4c9b0db0f", "sha256_in_prefix": "a59f0b0ef3635874109a4461ca44ff7a70d50696e814767bfaf721d4c9b0db0f", "size_in_bytes": 1541}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/msgpack/COPYING", "path_type": "hardlink", "sha256": "492dedba85da5872f78e6091bcd1fea474d660d35acb4dee964b8aab3f007427", "sha256_in_prefix": "492dedba85da5872f78e6091bcd1fea474d660d35acb4dee964b8aab3f007427", "size_in_bytes": 614}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/pkg_resources/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/platformdirs/LICENSE", "path_type": "hardlink", "sha256": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "sha256_in_prefix": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "size_in_bytes": 1089}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/pygments/LICENSE", "path_type": "hardlink", "sha256": "a9d66f1d526df02e29dce73436d34e56e8632f46c275bbdffc70569e882f9f17", "sha256_in_prefix": "a9d66f1d526df02e29dce73436d34e56e8632f46c275bbdffc70569e882f9f17", "size_in_bytes": 1331}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/pyproject_hooks/LICENSE", "path_type": "hardlink", "sha256": "1b22b049b5267d6dfc23a67bf4a84d8ec04b9fdfb1a51d360e42b4342c8b4154", "sha256_in_prefix": "1b22b049b5267d6dfc23a67bf4a84d8ec04b9fdfb1a51d360e42b4342c8b4154", "size_in_bytes": 1081}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/requests/LICENSE", "path_type": "hardlink", "sha256": "09e8a9bcec8067104652c168685ab0931e7868f9c8284b66f5ae6edae5f1130b", "sha256_in_prefix": "09e8a9bcec8067104652c168685ab0931e7868f9c8284b66f5ae6edae5f1130b", "size_in_bytes": 10142}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/resolvelib/LICENSE", "path_type": "hardlink", "sha256": "f388fd38cad13112c1dc0f669bbe80e7f84541edbafb72f3030d2ca7642c3c9d", "sha256_in_prefix": "f388fd38cad13112c1dc0f669bbe80e7f84541edbafb72f3030d2ca7642c3c9d", "size_in_bytes": 751}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/rich/LICENSE", "path_type": "hardlink", "sha256": "deed7c17a4318158190a3ea239cc879a5a50271cebb98ae7025f48fbe58dca15", "sha256_in_prefix": "deed7c17a4318158190a3ea239cc879a5a50271cebb98ae7025f48fbe58dca15", "size_in_bytes": 1056}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/tomli/LICENSE", "path_type": "hardlink", "sha256": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "sha256_in_prefix": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "size_in_bytes": 1072}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/tomli/LICENSE-HEADER", "path_type": "hardlink", "sha256": "97ce9330905a172dde870ee0361d89beb95ba3bd0f4545796aa91a8c01a43531", "sha256_in_prefix": "97ce9330905a172dde870ee0361d89beb95ba3bd0f4545796aa91a8c01a43531", "size_in_bytes": 121}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/tomli_w/LICENSE", "path_type": "hardlink", "sha256": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "sha256_in_prefix": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "size_in_bytes": 1072}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/truststore/LICENSE", "path_type": "hardlink", "sha256": "33be7b7e8fa4fd19b1760e1a8ed8a668bdab852c91b692dd41424bcb725a9fca", "sha256_in_prefix": "33be7b7e8fa4fd19b1760e1a8ed8a668bdab852c91b692dd41424bcb725a9fca", "size_in_bytes": 1086}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/licenses/src/pip/_vendor/urllib3/LICENSE.txt", "path_type": "hardlink", "sha256": "c37bf186e27cf9dbe9619e55edfe3cea7b30091ceb3da63c7dacbe0e6d77907b", "sha256_in_prefix": "c37bf186e27cf9dbe9619e55edfe3cea7b30091ceb3da63c7dacbe0e6d77907b", "size_in_bytes": 1115}, {"_path": "lib/python3.10/site-packages/pip-25.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "ceebae7b8927a3227e5303cf5e0f1f7b34bb542ad7250ac03fbcde36ec2f1508", "sha256_in_prefix": "ceebae7b8927a3227e5303cf5e0f1f7b34bb542ad7250ac03fbcde36ec2f1508", "size_in_bytes": 4}, {"_path": "lib/python3.10/site-packages/pip/__init__.py", "path_type": "hardlink", "sha256": "fe582ce4c7e9d2dec01ad23bb13570c4a5aabb3fef6a15961fd90a3b5709bac0", "sha256_in_prefix": "fe582ce4c7e9d2dec01ad23bb13570c4a5aabb3fef6a15961fd90a3b5709bac0", "size_in_bytes": 353}, {"_path": "lib/python3.10/site-packages/pip/__main__.py", "path_type": "hardlink", "sha256": "5b36e11d74db484ea0058d7d98d37d9b8b39a3fdfae4b3af4d84a0aa06dd0611", "sha256_in_prefix": "5b36e11d74db484ea0058d7d98d37d9b8b39a3fdfae4b3af4d84a0aa06dd0611", "size_in_bytes": 854}, {"_path": "lib/python3.10/site-packages/pip/__pip-runner__.py", "path_type": "hardlink", "sha256": "24ea04653c2bb6fee345a5c1920490280134e323727c59861f1aa91e2187bcbd", "sha256_in_prefix": "24ea04653c2bb6fee345a5c1920490280134e323727c59861f1aa91e2187bcbd", "size_in_bytes": 1450}, {"_path": "lib/python3.10/site-packages/pip/_internal/__init__.py", "path_type": "hardlink", "sha256": "4bb8bd0e7f5a4994b4306fb65abbdeddd57d4c898fcef427e638e1a7db7fb9fd", "sha256_in_prefix": "4bb8bd0e7f5a4994b4306fb65abbdeddd57d4c898fcef427e638e1a7db7fb9fd", "size_in_bytes": 511}, {"_path": "lib/python3.10/site-packages/pip/_internal/build_env.py", "path_type": "hardlink", "sha256": "e7ff8ee46d10b4278528701ecea033d08679fceeea09867e07175dbbd89da58b", "sha256_in_prefix": "e7ff8ee46d10b4278528701ecea033d08679fceeea09867e07175dbbd89da58b", "size_in_bytes": 11566}, {"_path": "lib/python3.10/site-packages/pip/_internal/cache.py", "path_type": "hardlink", "sha256": "ad65efb2e158ec680f111862369df264714629dbc3be46519f69f89979fc9408", "sha256_in_prefix": "ad65efb2e158ec680f111862369df264714629dbc3be46519f69f89979fc9408", "size_in_bytes": 10364}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__init__.py", "path_type": "hardlink", "sha256": "22a83fb4a03bef55ee30ed4fe2dfec0c79d228fce451bf43d03aae9c09b0fe4a", "sha256_in_prefix": "22a83fb4a03bef55ee30ed4fe2dfec0c79d228fce451bf43d03aae9c09b0fe4a", "size_in_bytes": 131}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/autocompletion.py", "path_type": "hardlink", "sha256": "646d9c334de795c36bb3e586fd21535b8ea2b31f6cd86a399540fff3e8afef4a", "sha256_in_prefix": "646d9c334de795c36bb3e586fd21535b8ea2b31f6cd86a399540fff3e8afef4a", "size_in_bytes": 7193}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/base_command.py", "path_type": "hardlink", "sha256": "d4dc7dd7d25116581444b8acf57609c2d6f21048d125afcd747c0cea206466f6", "sha256_in_prefix": "d4dc7dd7d25116581444b8acf57609c2d6f21048d125afcd747c0cea206466f6", "size_in_bytes": 8716}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/cmdoptions.py", "path_type": "hardlink", "sha256": "f7d6d6df1a5727c32be7d3866c6eddeef37b0863cb3f08b59d3984dde31cf251", "sha256_in_prefix": "f7d6d6df1a5727c32be7d3866c6eddeef37b0863cb3f08b59d3984dde31cf251", "size_in_bytes": 32032}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/command_context.py", "path_type": "hardlink", "sha256": "926bb711665b7c17a06b5a036a69c625303f51a7a384840db8c8f609598c5eed", "sha256_in_prefix": "926bb711665b7c17a06b5a036a69c625303f51a7a384840db8c8f609598c5eed", "size_in_bytes": 817}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/index_command.py", "path_type": "hardlink", "sha256": "00793a792a9ba1ac535db1b7bfd981ad57747422b532d5b8c373d5b9d9e3d161", "sha256_in_prefix": "00793a792a9ba1ac535db1b7bfd981ad57747422b532d5b8c373d5b9d9e3d161", "size_in_bytes": 5717}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/main.py", "path_type": "hardlink", "sha256": "2bd3eda51760eae06b54a93c4b6559d787c037490ff9c9c0d68f85b4908dfce4", "sha256_in_prefix": "2bd3eda51760eae06b54a93c4b6559d787c037490ff9c9c0d68f85b4908dfce4", "size_in_bytes": 2815}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/main_parser.py", "path_type": "hardlink", "sha256": "52e80f0fe845d56b4d41da30fd659db832d41f50ef125a5cec47945a352470da", "sha256_in_prefix": "52e80f0fe845d56b4d41da30fd659db832d41f50ef125a5cec47945a352470da", "size_in_bytes": 4329}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/parser.py", "path_type": "hardlink", "sha256": "b0039e6c16b9ff4ad1025981cbc9b230f1474196fe6db087d71a4824369768bb", "sha256_in_prefix": "b0039e6c16b9ff4ad1025981cbc9b230f1474196fe6db087d71a4824369768bb", "size_in_bytes": 10928}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/progress_bars.py", "path_type": "hardlink", "sha256": "9d14d63687fe1631dfbe2aef1025c887b4fb780ca74d454f4f21c435fa5b5a25", "sha256_in_prefix": "9d14d63687fe1631dfbe2aef1025c887b4fb780ca74d454f4f21c435fa5b5a25", "size_in_bytes": 4668}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/req_command.py", "path_type": "hardlink", "sha256": "a574ff8c0c1c98ef4f8c9d7e3e5d557af060af9a67c47b323d9501cc07ea420f", "sha256_in_prefix": "a574ff8c0c1c98ef4f8c9d7e3e5d557af060af9a67c47b323d9501cc07ea420f", "size_in_bytes": 13081}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/spinners.py", "path_type": "hardlink", "sha256": "109cd921937252d2658e9dfe5a372cc88aeac56f8752c7d6ce1b96f389ff4eac", "sha256_in_prefix": "109cd921937252d2658e9dfe5a372cc88aeac56f8752c7d6ce1b96f389ff4eac", "size_in_bytes": 7362}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/status_codes.py", "path_type": "hardlink", "sha256": "b0414751a5096eabfc880acbdc702d733b5666618e157d358537ac4b2b43121d", "sha256_in_prefix": "b0414751a5096eabfc880acbdc702d733b5666618e157d358537ac4b2b43121d", "size_in_bytes": 116}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__init__.py", "path_type": "hardlink", "sha256": "68d7826d0bab1968a17e142aec1a9a2d71ea5834348b7234e0e4bb9312baa654", "sha256_in_prefix": "68d7826d0bab1968a17e142aec1a9a2d71ea5834348b7234e0e4bb9312baa654", "size_in_bytes": 4026}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/cache.py", "path_type": "hardlink", "sha256": "3abacb4ba1091216bfe79c8f6bd7d368e6a89f0f95a47e3f955863c6e3930a14", "sha256_in_prefix": "3abacb4ba1091216bfe79c8f6bd7d368e6a89f0f95a47e3f955863c6e3930a14", "size_in_bytes": 8230}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/check.py", "path_type": "hardlink", "sha256": "85514141ecd0df38f813276859b1508ff69f3d4a6932deebf493c0958db653a6", "sha256_in_prefix": "85514141ecd0df38f813276859b1508ff69f3d4a6932deebf493c0958db653a6", "size_in_bytes": 2244}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/completion.py", "path_type": "hardlink", "sha256": "303c214ce06394ce1611b3a181b86b5a79439bbd748b814c8e8a774415d75c27", "sha256_in_prefix": "303c214ce06394ce1611b3a181b86b5a79439bbd748b814c8e8a774415d75c27", "size_in_bytes": 4530}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/configuration.py", "path_type": "hardlink", "sha256": "ea034e1ab5569ce2d4d79cd49c088db8e3217fbe9144866f09d543d1d7a03d19", "sha256_in_prefix": "ea034e1ab5569ce2d4d79cd49c088db8e3217fbe9144866f09d543d1d7a03d19", "size_in_bytes": 10105}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/debug.py", "path_type": "hardlink", "sha256": "ffc22a33c171d7f958d924eefeab29afadedb9f17bcf21490b26005edc73d0de", "sha256_in_prefix": "ffc22a33c171d7f958d924eefeab29afadedb9f17bcf21490b26005edc73d0de", "size_in_bytes": 6805}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/download.py", "path_type": "hardlink", "sha256": "186052c613915749ab69df124e03917ef8d11a1d6a4b6a65be95c535bc60729b", "sha256_in_prefix": "186052c613915749ab69df124e03917ef8d11a1d6a4b6a65be95c535bc60729b", "size_in_bytes": 5249}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/freeze.py", "path_type": "hardlink", "sha256": "7f1a16f0001cf9b02a07f7d774dab656767725f5a414c83e6d1e8b71d0d53bb0", "sha256_in_prefix": "7f1a16f0001cf9b02a07f7d774dab656767725f5a414c83e6d1e8b71d0d53bb0", "size_in_bytes": 3099}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/hash.py", "path_type": "hardlink", "sha256": "18ef6944ddf05c2da441aa2f2b9ed368b60131cdc896d387f763ba404c3a604d", "sha256_in_prefix": "18ef6944ddf05c2da441aa2f2b9ed368b60131cdc896d387f763ba404c3a604d", "size_in_bytes": 1679}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/help.py", "path_type": "hardlink", "sha256": "073dcb7233505e4cf80aefffa4be0267cea8e3e1cd2d98f535959d949863baed", "sha256_in_prefix": "073dcb7233505e4cf80aefffa4be0267cea8e3e1cd2d98f535959d949863baed", "size_in_bytes": 1108}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/index.py", "path_type": "hardlink", "sha256": "f06301548e4dbe14511c1494ab6ed8c43204d360e9bdd27feaa88116019dd5ca", "sha256_in_prefix": "f06301548e4dbe14511c1494ab6ed8c43204d360e9bdd27feaa88116019dd5ca", "size_in_bytes": 5243}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/inspect.py", "path_type": "hardlink", "sha256": "a209b8513ecb468f1b210716512d48880db941d0f85472daec968f028743b6d3", "sha256_in_prefix": "a209b8513ecb468f1b210716512d48880db941d0f85472daec968f028743b6d3", "size_in_bytes": 3177}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/install.py", "path_type": "hardlink", "sha256": "0083be526e3d7b71aa30e9364a61c2b78e40d16d3e621297729af9a248c787cd", "sha256_in_prefix": "0083be526e3d7b71aa30e9364a61c2b78e40d16d3e621297729af9a248c787cd", "size_in_bytes": 30080}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/list.py", "path_type": "hardlink", "sha256": "238647eb4e04e60a5c44ec445c0edec9a3441615f1dd5155aafa6c733fcfb3f0", "sha256_in_prefix": "238647eb4e04e60a5c44ec445c0edec9a3441615f1dd5155aafa6c733fcfb3f0", "size_in_bytes": 13514}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/lock.py", "path_type": "hardlink", "sha256": "4b15c69d4d841fe4c6f9fb3d7ee64702e30e1f5e4bcf2d4aedefca278bed4abd", "sha256_in_prefix": "4b15c69d4d841fe4c6f9fb3d7ee64702e30e1f5e4bcf2d4aedefca278bed4abd", "size_in_bytes": 5917}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/search.py", "path_type": "hardlink", "sha256": "cdb32c5ff6004a3ea45c0e972018130efd1b18ae75c46f825772329d925c13e7", "sha256_in_prefix": "cdb32c5ff6004a3ea45c0e972018130efd1b18ae75c46f825772329d925c13e7", "size_in_bytes": 5782}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/show.py", "path_type": "hardlink", "sha256": "a0b54921f2969832a6d12b101848b7a68ccd8aaad78d3adab3f7db05260aa410", "sha256_in_prefix": "a0b54921f2969832a6d12b101848b7a68ccd8aaad78d3adab3f7db05260aa410", "size_in_bytes": 8066}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/uninstall.py", "path_type": "hardlink", "sha256": "0ac3a286abdbe9903a3baecbef4a177a82c778e7cdccc33cf07f60fbd6a8720c", "sha256_in_prefix": "0ac3a286abdbe9903a3baecbef4a177a82c778e7cdccc33cf07f60fbd6a8720c", "size_in_bytes": 3868}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/wheel.py", "path_type": "hardlink", "sha256": "bdc3609e1c708a5a1c45024405d590bc9f2abced117d69b3f4bc2b50169dbd81", "sha256_in_prefix": "bdc3609e1c708a5a1c45024405d590bc9f2abced117d69b3f4bc2b50169dbd81", "size_in_bytes": 6322}, {"_path": "lib/python3.10/site-packages/pip/_internal/configuration.py", "path_type": "hardlink", "sha256": "068990de90bce09eb30970d0a4d0ff3a6158ee6b9b13d70c05956884d53332f6", "sha256_in_prefix": "068990de90bce09eb30970d0a4d0ff3a6158ee6b9b13d70c05956884d53332f6", "size_in_bytes": 14587}, {"_path": "lib/python3.10/site-packages/pip/_internal/distributions/__init__.py", "path_type": "hardlink", "sha256": "1eaea4b7a8170608cd8ade614d358b03378234e2a807e374a46612a9e86b962f", "sha256_in_prefix": "1eaea4b7a8170608cd8ade614d358b03378234e2a807e374a46612a9e86b962f", "size_in_bytes": 858}, {"_path": "lib/python3.10/site-packages/pip/_internal/distributions/base.py", "path_type": "hardlink", "sha256": "97e39308022cdb996c6a97a303a218a4f67148ce7e0444f8b1d64392a97c8e26", "sha256_in_prefix": "97e39308022cdb996c6a97a303a218a4f67148ce7e0444f8b1d64392a97c8e26", "size_in_bytes": 1830}, {"_path": "lib/python3.10/site-packages/pip/_internal/distributions/installed.py", "path_type": "hardlink", "sha256": "92020413fd4dce36712c1482fafe6ceb8b8e15995512ddda3eb8d3b4beb1d976", "sha256_in_prefix": "92020413fd4dce36712c1482fafe6ceb8b8e15995512ddda3eb8d3b4beb1d976", "size_in_bytes": 929}, {"_path": "lib/python3.10/site-packages/pip/_internal/distributions/sdist.py", "path_type": "hardlink", "sha256": "81882bcc8d25b2d1c944ac1e39d2ff9ba2eda83c6d7eea3fc822fd1e3987fb1c", "sha256_in_prefix": "81882bcc8d25b2d1c944ac1e39d2ff9ba2eda83c6d7eea3fc822fd1e3987fb1c", "size_in_bytes": 6952}, {"_path": "lib/python3.10/site-packages/pip/_internal/distributions/wheel.py", "path_type": "hardlink", "sha256": "fc76c6d0e7a117c7708f8517fb157ffed5cbc203eeb3d3a33047f635346a05b1", "sha256_in_prefix": "fc76c6d0e7a117c7708f8517fb157ffed5cbc203eeb3d3a33047f635346a05b1", "size_in_bytes": 1364}, {"_path": "lib/python3.10/site-packages/pip/_internal/exceptions.py", "path_type": "hardlink", "sha256": "6d14c49fa265c3ebf11e38b1b62bb42bbf635c9bb45f91550b42fad8175bd0a2", "sha256_in_prefix": "6d14c49fa265c3ebf11e38b1b62bb42bbf635c9bb45f91550b42fad8175bd0a2", "size_in_bytes": 28974}, {"_path": "lib/python3.10/site-packages/pip/_internal/index/__init__.py", "path_type": "hardlink", "sha256": "b73c0c1ff7e141eb9bc0ca877528af6ac83571181349b360d8288c567ccc9b25", "sha256_in_prefix": "b73c0c1ff7e141eb9bc0ca877528af6ac83571181349b360d8288c567ccc9b25", "size_in_bytes": 29}, {"_path": "lib/python3.10/site-packages/pip/_internal/index/collector.py", "path_type": "hardlink", "sha256": "3c2077b61556462481a301ada6fd5e9483c573e1af12a85988d8190fb6f4bc17", "sha256_in_prefix": "3c2077b61556462481a301ada6fd5e9483c573e1af12a85988d8190fb6f4bc17", "size_in_bytes": 16185}, {"_path": "lib/python3.10/site-packages/pip/_internal/index/package_finder.py", "path_type": "hardlink", "sha256": "29dd8599624501c120d92cb6a531905fc58f142f01f54ff76f554aee2f2cc7da", "sha256_in_prefix": "29dd8599624501c120d92cb6a531905fc58f142f01f54ff76f554aee2f2cc7da", "size_in_bytes": 38827}, {"_path": "lib/python3.10/site-packages/pip/_internal/index/sources.py", "path_type": "hardlink", "sha256": "9d72643a384bcbe3b616caca53d448a823aa818d8fb28296c9bb598e3460a94d", "sha256_in_prefix": "9d72643a384bcbe3b616caca53d448a823aa818d8fb28296c9bb598e3460a94d", "size_in_bytes": 8639}, {"_path": "lib/python3.10/site-packages/pip/_internal/locations/__init__.py", "path_type": "hardlink", "sha256": "d920035f41abf41229c75f403bb15eabcf673a60501846e5d485a30699da13d1", "sha256_in_prefix": "d920035f41abf41229c75f403bb15eabcf673a60501846e5d485a30699da13d1", "size_in_bytes": 14185}, {"_path": "lib/python3.10/site-packages/pip/_internal/locations/_distutils.py", "path_type": "hardlink", "sha256": "8e9163e15d34ac3f48477bc0f53aab1a4c1c74d545739f0bb028596af81ef496", "sha256_in_prefix": "8e9163e15d34ac3f48477bc0f53aab1a4c1c74d545739f0bb028596af81ef496", "size_in_bytes": 5975}, {"_path": "lib/python3.10/site-packages/pip/_internal/locations/_sysconfig.py", "path_type": "hardlink", "sha256": "3617048b5ff6e70f5c4d3707e11c8e8c3e141d6e888e4b34b8acb53cbd7f8fff", "sha256_in_prefix": "3617048b5ff6e70f5c4d3707e11c8e8c3e141d6e888e4b34b8acb53cbd7f8fff", "size_in_bytes": 7716}, {"_path": "lib/python3.10/site-packages/pip/_internal/locations/base.py", "path_type": "hardlink", "sha256": "0089a36095b13ad0e473428a73a6381b3ebbedc83dd5c68c0382fde01f456448", "sha256_in_prefix": "0089a36095b13ad0e473428a73a6381b3ebbedc83dd5c68c0382fde01f456448", "size_in_bytes": 2550}, {"_path": "lib/python3.10/site-packages/pip/_internal/main.py", "path_type": "hardlink", "sha256": "d5c1ea8ec7c50ab3057f7079b70ce87314c951d1cc2e85d4a72e6526816a522f", "sha256_in_prefix": "d5c1ea8ec7c50ab3057f7079b70ce87314c951d1cc2e85d4a72e6526816a522f", "size_in_bytes": 338}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/__init__.py", "path_type": "hardlink", "sha256": "6b5f41d3422c5f6e64841b7ca165b2ebf90a82308ce337a1f85a98b5e08e1700", "sha256_in_prefix": "6b5f41d3422c5f6e64841b7ca165b2ebf90a82308ce337a1f85a98b5e08e1700", "size_in_bytes": 5714}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/_json.py", "path_type": "hardlink", "sha256": "84dbe73073972c0c8d9738ab5a13cbf4dc760af0aa6b589199ae8eb2ad587d5f", "sha256_in_prefix": "84dbe73073972c0c8d9738ab5a13cbf4dc760af0aa6b589199ae8eb2ad587d5f", "size_in_bytes": 2711}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/base.py", "path_type": "hardlink", "sha256": "046b8c7a795c413f22ee3f62725adf742def4b082f86bf208e7f79e5c0b2d7ab", "sha256_in_prefix": "046b8c7a795c413f22ee3f62725adf742def4b082f86bf208e7f79e5c0b2d7ab", "size_in_bytes": 25420}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/importlib/__init__.py", "path_type": "hardlink", "sha256": "8d4522768c671dc7c84c71da0161b51b68b97dd058925bffb89723a36c7b5581", "sha256_in_prefix": "8d4522768c671dc7c84c71da0161b51b68b97dd058925bffb89723a36c7b5581", "size_in_bytes": 135}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/importlib/_compat.py", "path_type": "hardlink", "sha256": "b27795878ffa5b14192b8963765df2955b8ffaad2db52a9b825f665add079ce8", "sha256_in_prefix": "b27795878ffa5b14192b8963765df2955b8ffaad2db52a9b825f665add079ce8", "size_in_bytes": 2804}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/importlib/_dists.py", "path_type": "hardlink", "sha256": "68e520097fc0b40f01fa558bbbe1df5c0fa2bcc2563711cf028855729423da0e", "sha256_in_prefix": "68e520097fc0b40f01fa558bbbe1df5c0fa2bcc2563711cf028855729423da0e", "size_in_bytes": 8259}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/importlib/_envs.py", "path_type": "hardlink", "sha256": "1f7a952d7561e0b5afacfbeefde9177f775f6edc2796134940fda9c57a5c71f5", "sha256_in_prefix": "1f7a952d7561e0b5afacfbeefde9177f775f6edc2796134940fda9c57a5c71f5", "size_in_bytes": 5333}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/pkg_resources.py", "path_type": "hardlink", "sha256": "34eefa66b7d1dbe2ca253c9a5eb990a0686624c02b00bbda72b9591d58920d3f", "sha256_in_prefix": "34eefa66b7d1dbe2ca253c9a5eb990a0686624c02b00bbda72b9591d58920d3f", "size_in_bytes": 10544}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__init__.py", "path_type": "hardlink", "sha256": "023982101c57fcc1fd7ff8d58c818d08524a60261e4847b5f32caf371e2e44a4", "sha256_in_prefix": "023982101c57fcc1fd7ff8d58c818d08524a60261e4847b5f32caf371e2e44a4", "size_in_bytes": 62}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/candidate.py", "path_type": "hardlink", "sha256": "cf380546ec3f9163e32a91b0ecb0b4654303d8243611b7ab50862cf22ce37420", "sha256_in_prefix": "cf380546ec3f9163e32a91b0ecb0b4654303d8243611b7ab50862cf22ce37420", "size_in_bytes": 753}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/direct_url.py", "path_type": "hardlink", "sha256": "e0d31669cbbf4333cf591102d6d7bbbfa59fbfed87908e2dbd2245f820602e1e", "sha256_in_prefix": "e0d31669cbbf4333cf591102d6d7bbbfa59fbfed87908e2dbd2245f820602e1e", "size_in_bytes": 6555}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/format_control.py", "path_type": "hardlink", "sha256": "3f07a6606d4bdbb04cd1fd4a3fad6b9b6e304a110d172c6a943d535aedf86a57", "sha256_in_prefix": "3f07a6606d4bdbb04cd1fd4a3fad6b9b6e304a110d172c6a943d535aedf86a57", "size_in_bytes": 2471}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/index.py", "path_type": "hardlink", "sha256": "b589cbf28c468b8692356babd261bc0c03fbac2eb2ba16bf33024ef31c3472b2", "sha256_in_prefix": "b589cbf28c468b8692356babd261bc0c03fbac2eb2ba16bf33024ef31c3472b2", "size_in_bytes": 1030}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/installation_report.py", "path_type": "hardlink", "sha256": "72a7d627ddd3842c6371a72a496af23820f65ed227d4266b83367102fe45419d", "sha256_in_prefix": "72a7d627ddd3842c6371a72a496af23820f65ed227d4266b83367102fe45419d", "size_in_bytes": 2839}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/link.py", "path_type": "hardlink", "sha256": "878948f0c68303b0d3231209fcd80313ae04138875b17dc0076dd8d50bbc5be9", "sha256_in_prefix": "878948f0c68303b0d3231209fcd80313ae04138875b17dc0076dd8d50bbc5be9", "size_in_bytes": 21793}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/pylock.py", "path_type": "hardlink", "sha256": "56669aef580e495d196334605a2226e0ac156c295a6a131cbaf28290ffd9ce70", "sha256_in_prefix": "56669aef580e495d196334605a2226e0ac156c295a6a131cbaf28290ffd9ce70", "size_in_bytes": 6211}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/scheme.py", "path_type": "hardlink", "sha256": "3da9261c93377bc38e592645b5fcf5033edfd6678e3499e41ae431165b77c011", "sha256_in_prefix": "3da9261c93377bc38e592645b5fcf5033edfd6678e3499e41ae431165b77c011", "size_in_bytes": 575}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/search_scope.py", "path_type": "hardlink", "sha256": "d61c54d8856c01a2d9563a7409bcc96d8698cc2c6fef6fd06e0dc92f4aa15e8d", "sha256_in_prefix": "d61c54d8856c01a2d9563a7409bcc96d8698cc2c6fef6fd06e0dc92f4aa15e8d", "size_in_bytes": 4507}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/selection_prefs.py", "path_type": "hardlink", "sha256": "960632a385bc95bdb6c2c631d84941041d1cbd236505582e7300732f8ddd7f31", "sha256_in_prefix": "960632a385bc95bdb6c2c631d84941041d1cbd236505582e7300732f8ddd7f31", "size_in_bytes": 2016}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/target_python.py", "path_type": "hardlink", "sha256": "2347854be7a26b793086b3af82ca61159b4d001d88c1767d4abf5fa7a22304fe", "sha256_in_prefix": "2347854be7a26b793086b3af82ca61159b4d001d88c1767d4abf5fa7a22304fe", "size_in_bytes": 4243}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/wheel.py", "path_type": "hardlink", "sha256": "c563b42be61a9d22f739f31950dd601e511457460939560c438c2bc2603d662b", "sha256_in_prefix": "c563b42be61a9d22f739f31950dd601e511457460939560c438c2bc2603d662b", "size_in_bytes": 5526}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/__init__.py", "path_type": "hardlink", "sha256": "14ccb4e8ffffcba8cc8d473ccf765c41d285fa9999db3335e3fbc17873c68542", "sha256_in_prefix": "14ccb4e8ffffcba8cc8d473ccf765c41d285fa9999db3335e3fbc17873c68542", "size_in_bytes": 49}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/auth.py", "path_type": "hardlink", "sha256": "b80c1118062756d80d4999b81cc0b704c00292003b92ee26f30ba9897e8ba4af", "sha256_in_prefix": "b80c1118062756d80d4999b81cc0b704c00292003b92ee26f30ba9897e8ba4af", "size_in_bytes": 20681}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/cache.py", "path_type": "hardlink", "sha256": "bb92ed41b9c2ccc53abc54f479451c61eea22a2c002ccaab9f58d4663e02a9a6", "sha256_in_prefix": "bb92ed41b9c2ccc53abc54f479451c61eea22a2c002ccaab9f58d4663e02a9a6", "size_in_bytes": 5302}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/download.py", "path_type": "hardlink", "sha256": "1e0b05bd390f0dd820d3351e868b1efc9fb9e36fbd474169ca2a51c39061c403", "sha256_in_prefix": "1e0b05bd390f0dd820d3351e868b1efc9fb9e36fbd474169ca2a51c39061c403", "size_in_bytes": 12682}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/lazy_wheel.py", "path_type": "hardlink", "sha256": "e47effb3efc02ba6ebd4ae4d15cc94b22a1c2bd13abf0ac9639933c2332fd043", "sha256_in_prefix": "e47effb3efc02ba6ebd4ae4d15cc94b22a1c2bd13abf0ac9639933c2332fd043", "size_in_bytes": 7651}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/session.py", "path_type": "hardlink", "sha256": "784f9550824653d61e79a572eed540bcc4568a032ccae00ca7192395ba6d6e3a", "sha256_in_prefix": "784f9550824653d61e79a572eed540bcc4568a032ccae00ca7192395ba6d6e3a", "size_in_bytes": 19188}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/utils.py", "path_type": "hardlink", "sha256": "002b177759aca8d0a2747557b2eecb1d44abf0d81aca970e290e0a1be67fc093", "sha256_in_prefix": "002b177759aca8d0a2747557b2eecb1d44abf0d81aca970e290e0a1be67fc093", "size_in_bytes": 4091}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/xmlrpc.py", "path_type": "hardlink", "sha256": "ffe467937bce7dff2e17d8401a64fa48b00b7e563580c05c6c6c12d767dbfd8e", "sha256_in_prefix": "ffe467937bce7dff2e17d8401a64fa48b00b7e563580c05c6c6c12d767dbfd8e", "size_in_bytes": 1830}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/build_tracker.py", "path_type": "hardlink", "sha256": "5b76f972690c58f684e902307f3b136b2268efe3a5c451d60f17cfb9ac7529c1", "sha256_in_prefix": "5b76f972690c58f684e902307f3b136b2268efe3a5c451d60f17cfb9ac7529c1", "size_in_bytes": 4771}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/metadata.py", "path_type": "hardlink", "sha256": "20d1da7a245f3a22d80970297c3351a3d0b0db12385704dcd0a22dbdf75f3a39", "sha256_in_prefix": "20d1da7a245f3a22d80970297c3351a3d0b0db12385704dcd0a22dbdf75f3a39", "size_in_bytes": 1421}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/metadata_editable.py", "path_type": "hardlink", "sha256": "a16b9d32c9e3cb89683bf272ee0e0df67c6c9da117fe20e5551802cbba6ed6bb", "sha256_in_prefix": "a16b9d32c9e3cb89683bf272ee0e0df67c6c9da117fe20e5551802cbba6ed6bb", "size_in_bytes": 1509}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/metadata_legacy.py", "path_type": "hardlink", "sha256": "c2ff1c140d304ea17ad89966f50c25a1866ca1f3b243bb160419af09c56f9f59", "sha256_in_prefix": "c2ff1c140d304ea17ad89966f50c25a1866ca1f3b243bb160419af09c56f9f59", "size_in_bytes": 2189}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/wheel.py", "path_type": "hardlink", "sha256": "b680491396ad2891ac49c918e73b55010a51b2cdb97f4e3d7fe9c96a226c883f", "sha256_in_prefix": "b680491396ad2891ac49c918e73b55010a51b2cdb97f4e3d7fe9c96a226c883f", "size_in_bytes": 1080}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/wheel_editable.py", "path_type": "hardlink", "sha256": "0e3c7b859aa17a381851c7500f9a789a7ec0545377550edb392b3b6fdd13b4e2", "sha256_in_prefix": "0e3c7b859aa17a381851c7500f9a789a7ec0545377550edb392b3b6fdd13b4e2", "size_in_bytes": 1422}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/wheel_legacy.py", "path_type": "hardlink", "sha256": "2da1e91b8feceb8d7988b8ee4dbeac78edc9209ef4f7bc90508b61726a6a68e6", "sha256_in_prefix": "2da1e91b8feceb8d7988b8ee4dbeac78edc9209ef4f7bc90508b61726a6a68e6", "size_in_bytes": 3616}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/check.py", "path_type": "hardlink", "sha256": "c82d975ad87a89e846184fdf8fb5d124b8d5281b13206dd9a16464162deb3b07", "sha256_in_prefix": "c82d975ad87a89e846184fdf8fb5d124b8d5281b13206dd9a16464162deb3b07", "size_in_bytes": 5894}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/freeze.py", "path_type": "hardlink", "sha256": "3c3758fb2fd9b596492c029c6963c8191280196ec35d1e3c7f468c454d23ec10", "sha256_in_prefix": "3c3758fb2fd9b596492c029c6963c8191280196ec35d1e3c7f468c454d23ec10", "size_in_bytes": 9854}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/install/__init__.py", "path_type": "hardlink", "sha256": "6a4f941137103ca94565a5a860a5aee505576e9141be0d2c5dcde2d0ee2f4986", "sha256_in_prefix": "6a4f941137103ca94565a5a860a5aee505576e9141be0d2c5dcde2d0ee2f4986", "size_in_bytes": 50}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/install/editable_legacy.py", "path_type": "hardlink", "sha256": "cd2e879bef521dcad5b80ffd22b73d41cf0222814b0cc6624af8b9ae2cd97b7e", "sha256_in_prefix": "cd2e879bef521dcad5b80ffd22b73d41cf0222814b0cc6624af8b9ae2cd97b7e", "size_in_bytes": 1311}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/install/wheel.py", "path_type": "hardlink", "sha256": "f1a7a9c710059a7cd916d70c0affb52384ff99a12441de215d9ced616138c91d", "sha256_in_prefix": "f1a7a9c710059a7cd916d70c0affb52384ff99a12441de215d9ced616138c91d", "size_in_bytes": 27956}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/prepare.py", "path_type": "hardlink", "sha256": "81d6d05008f12eaab3f0fd8a15e9b638427fe95dda4eea334248812df0825405", "sha256_in_prefix": "81d6d05008f12eaab3f0fd8a15e9b638427fe95dda4eea334248812df0825405", "size_in_bytes": 28613}, {"_path": "lib/python3.10/site-packages/pip/_internal/pyproject.py", "path_type": "hardlink", "sha256": "4fbdaacf46ee698d2cfa682b5cb514d6c38d7f8464f7b749bb4aa99e2042194f", "sha256_in_prefix": "4fbdaacf46ee698d2cfa682b5cb514d6c38d7f8464f7b749bb4aa99e2042194f", "size_in_bytes": 7233}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/__init__.py", "path_type": "hardlink", "sha256": "bccd1b580977072e402d12175a629d90c71ad7c2907db197549666dc8829c268", "sha256_in_prefix": "bccd1b580977072e402d12175a629d90c71ad7c2907db197549666dc8829c268", "size_in_bytes": 3122}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/constructors.py", "path_type": "hardlink", "sha256": "4252e3048839c4fc72431fe118607b173e76eebb805c2ea76d532819f5f33673", "sha256_in_prefix": "4252e3048839c4fc72431fe118607b173e76eebb805c2ea76d532819f5f33673", "size_in_bytes": 18320}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/req_dependency_group.py", "path_type": "hardlink", "sha256": "d3211009468ee41cdaeba6370f9a3d2517f4a88239420091ba09f5c796918af0", "sha256_in_prefix": "d3211009468ee41cdaeba6370f9a3d2517f4a88239420091ba09f5c796918af0", "size_in_bytes": 2618}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/req_file.py", "path_type": "hardlink", "sha256": "8ffd4f7010ced1a29bf298e344cb1942bc2db3960dacab2b1e33f2579e97a07a", "sha256_in_prefix": "8ffd4f7010ced1a29bf298e344cb1942bc2db3960dacab2b1e33f2579e97a07a", "size_in_bytes": 20161}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/req_install.py", "path_type": "hardlink", "sha256": "3782f157d2c78a68257b9cc490d164f5649f8b9d4a5ef2bdcbbde6bd897dba28", "sha256_in_prefix": "3782f157d2c78a68257b9cc490d164f5649f8b9d4a5ef2bdcbbde6bd897dba28", "size_in_bytes": 35718}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/req_set.py", "path_type": "hardlink", "sha256": "6b092a2179d80383eb9ac8f441bdf386a75b61499777ed68d0ff8a6779af450b", "sha256_in_prefix": "6b092a2179d80383eb9ac8f441bdf386a75b61499777ed68d0ff8a6779af450b", "size_in_bytes": 2828}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/req_uninstall.py", "path_type": "hardlink", "sha256": "74298e1edfbd45a241abddb52f8b4c1f73e62010deb46a659db8d1297986b74d", "sha256_in_prefix": "74298e1edfbd45a241abddb52f8b4c1f73e62010deb46a659db8d1297986b74d", "size_in_bytes": 24099}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/base.py", "path_type": "hardlink", "sha256": "448b2a48fefda253dd3a0b4f296fa8390dfae08095a297a103a45f1a445f7b6b", "sha256_in_prefix": "448b2a48fefda253dd3a0b4f296fa8390dfae08095a297a103a45f1a445f7b6b", "size_in_bytes": 577}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/legacy/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/legacy/resolver.py", "path_type": "hardlink", "sha256": "6f052a13ae9eb73d9b70f69bab179dd7cfa2caaa9bfa4c7712bd9a4fa19e5096", "sha256_in_prefix": "6f052a13ae9eb73d9b70f69bab179dd7cfa2caaa9bfa4c7712bd9a4fa19e5096", "size_in_bytes": 24060}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/base.py", "path_type": "hardlink", "sha256": "fc0a0fd195a569272df024439f6a25dc26cd9f8cc3b6787fd334068d70120ca2", "sha256_in_prefix": "fc0a0fd195a569272df024439f6a25dc26cd9f8cc3b6787fd334068d70120ca2", "size_in_bytes": 5047}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/candidates.py", "path_type": "hardlink", "sha256": "75b968b0370cba6753d3be58f03d725e226b585ff00e02067bff21510227793c", "sha256_in_prefix": "75b968b0370cba6753d3be58f03d725e226b585ff00e02067bff21510227793c", "size_in_bytes": 20208}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/factory.py", "path_type": "hardlink", "sha256": "5dd0a92eae7da1520730c7a47367200fb8a67150f4d3f8a84e7bbdfe4de2ab71", "sha256_in_prefix": "5dd0a92eae7da1520730c7a47367200fb8a67150f4d3f8a84e7bbdfe4de2ab71", "size_in_bytes": 32577}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/found_candidates.py", "path_type": "hardlink", "sha256": "f1b6580c264b5d274b1f2fecd68e5fe2bd791e62afa85521cc1510385db52ebe", "sha256_in_prefix": "f1b6580c264b5d274b1f2fecd68e5fe2bd791e62afa85521cc1510385db52ebe", "size_in_bytes": 6018}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/provider.py", "path_type": "hardlink", "sha256": "d53242da8ec5d3669331aa2a6b4032cf8e4fac4fa94395ec8601949b07844a49", "sha256_in_prefix": "d53242da8ec5d3669331aa2a6b4032cf8e4fac4fa94395ec8601949b07844a49", "size_in_bytes": 11144}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/reporter.py", "path_type": "hardlink", "sha256": "6ad8938756e729cf8a7c6f38d67470ddd93459b3a1aff2fc96e247718d09a12b", "sha256_in_prefix": "6ad8938756e729cf8a7c6f38d67470ddd93459b3a1aff2fc96e247718d09a12b", "size_in_bytes": 3270}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/requirements.py", "path_type": "hardlink", "sha256": "cf48179967e8d37ca73a7845f24a63e52c9c82ba1eac3850574556ce600a01f8", "sha256_in_prefix": "cf48179967e8d37ca73a7845f24a63e52c9c82ba1eac3850574556ce600a01f8", "size_in_bytes": 8076}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/resolver.py", "path_type": "hardlink", "sha256": "7f47e663307d1b8c16c6cb1f8f8a9eac38bf17ff2f8c06983db18b6fb37bb50c", "sha256_in_prefix": "7f47e663307d1b8c16c6cb1f8f8a9eac38bf17ff2f8c06983db18b6fb37bb50c", "size_in_bytes": 13617}, {"_path": "lib/python3.10/site-packages/pip/_internal/self_outdated_check.py", "path_type": "hardlink", "sha256": "c1f8a4666706548ae77a92f8d77fb4ae74378de380d2e9ce28f95ed0ebf0a21a", "sha256_in_prefix": "c1f8a4666706548ae77a92f8d77fb4ae74378de380d2e9ce28f95ed0ebf0a21a", "size_in_bytes": 8326}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/_jaraco_text.py", "path_type": "hardlink", "sha256": "335e6e50f221e4da4fd6d754181c516aeeaad59004b48f3e5f22c4113b1c15f1", "sha256_in_prefix": "335e6e50f221e4da4fd6d754181c516aeeaad59004b48f3e5f22c4113b1c15f1", "size_in_bytes": 3350}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/_log.py", "path_type": "hardlink", "sha256": "fa31cb384fd31da673e4115c0a7a122fd11802d2749d77a6e3db3da1fe23bcac", "sha256_in_prefix": "fa31cb384fd31da673e4115c0a7a122fd11802d2749d77a6e3db3da1fe23bcac", "size_in_bytes": 1015}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/appdirs.py", "path_type": "hardlink", "sha256": "2ebcc33d930a561d2bb9bb42c7dbeedd79596cb092ba0e954a3e10b2fb7ae810", "sha256_in_prefix": "2ebcc33d930a561d2bb9bb42c7dbeedd79596cb092ba0e954a3e10b2fb7ae810", "size_in_bytes": 1681}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/compat.py", "path_type": "hardlink", "sha256": "0bd2c75c900a930007f079f79cf933f4460adeba8f05e3bf21790e1b6cf3b1d4", "sha256_in_prefix": "0bd2c75c900a930007f079f79cf933f4460adeba8f05e3bf21790e1b6cf3b1d4", "size_in_bytes": 2514}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/compatibility_tags.py", "path_type": "hardlink", "sha256": "0e23522eaa6eaee5d46a6190c0e2765990720c62d319a5e2f4ef977fc7ce8b7e", "sha256_in_prefix": "0e23522eaa6eaee5d46a6190c0e2765990720c62d319a5e2f4ef977fc7ce8b7e", "size_in_bytes": 6630}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/datetime.py", "path_type": "hardlink", "sha256": "1addbd325e13a0f48cf3c8f9e22bb8dd62adad4f40fa6a0fe1098c8a2ab379d5", "sha256_in_prefix": "1addbd325e13a0f48cf3c8f9e22bb8dd62adad4f40fa6a0fe1098c8a2ab379d5", "size_in_bytes": 241}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/deprecation.py", "path_type": "hardlink", "sha256": "1d586fc8ee6a89115c1bcf0f859969ffceea74a40dc0f4d42081d6b6c4d1db22", "sha256_in_prefix": "1d586fc8ee6a89115c1bcf0f859969ffceea74a40dc0f4d42081d6b6c4d1db22", "size_in_bytes": 3696}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/direct_url_helpers.py", "path_type": "hardlink", "sha256": "b6d2afe06314aa54703cfa20f7f094a29cba483828c5520bcde049ce07e7dad8", "sha256_in_prefix": "b6d2afe06314aa54703cfa20f7f094a29cba483828c5520bcde049ce07e7dad8", "size_in_bytes": 3200}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/egg_link.py", "path_type": "hardlink", "sha256": "6167ecadb99f72b7d682a418cba3ae223b326fd21f2f5abfdafe33b26b355a32", "sha256_in_prefix": "6167ecadb99f72b7d682a418cba3ae223b326fd21f2f5abfdafe33b26b355a32", "size_in_bytes": 2459}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/entrypoints.py", "path_type": "hardlink", "sha256": "b8f8c0c9284a39b768b63423254ce9ad0eabdf142f0c86705187c71ea67b0e89", "sha256_in_prefix": "b8f8c0c9284a39b768b63423254ce9ad0eabdf142f0c86705187c71ea67b0e89", "size_in_bytes": 3324}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/filesystem.py", "path_type": "hardlink", "sha256": "76ee2538e953381ab6d95e647f4c97332768d4a5360acf9c32d62cddb124d777", "sha256_in_prefix": "76ee2538e953381ab6d95e647f4c97332768d4a5360acf9c32d62cddb124d777", "size_in_bytes": 4988}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/filetypes.py", "path_type": "hardlink", "sha256": "b0431adfca9aaa3bf1d5989ddce0805236b7d4138153e512b9230f06f537ca3a", "sha256_in_prefix": "b0431adfca9aaa3bf1d5989ddce0805236b7d4138153e512b9230f06f537ca3a", "size_in_bytes": 689}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/glibc.py", "path_type": "hardlink", "sha256": "b0487c44924b612751bd3a803b84c754f3c0f9848354b0f8488f52a3e6f15f55", "sha256_in_prefix": "b0487c44924b612751bd3a803b84c754f3c0f9848354b0f8488f52a3e6f15f55", "size_in_bytes": 3726}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/hashes.py", "path_type": "hardlink", "sha256": "777d942357a7f27caa67375942fc5455d7de0687b80035b1ec7b6b20ce3e5d0e", "sha256_in_prefix": "777d942357a7f27caa67375942fc5455d7de0687b80035b1ec7b6b20ce3e5d0e", "size_in_bytes": 4998}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/logging.py", "path_type": "hardlink", "sha256": "46d45eed5a7408e0b85017b061d7ca89c5e30939875e90d91dd45e4f326f07bf", "sha256_in_prefix": "46d45eed5a7408e0b85017b061d7ca89c5e30939875e90d91dd45e4f326f07bf", "size_in_bytes": 12108}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/misc.py", "path_type": "hardlink", "sha256": "d63129aa37ea62643a2b70f8fcef315d23e7f1a11f1f6b8c3a534ceca3ef4ab8", "sha256_in_prefix": "d63129aa37ea62643a2b70f8fcef315d23e7f1a11f1f6b8c3a534ceca3ef4ab8", "size_in_bytes": 23374}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/packaging.py", "path_type": "hardlink", "sha256": "b39b6952616e9b05741fd2524f3af2ac863827040cf29686b7b4a6ede370b766", "sha256_in_prefix": "b39b6952616e9b05741fd2524f3af2ac863827040cf29686b7b4a6ede370b766", "size_in_bytes": 1601}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/retry.py", "path_type": "hardlink", "sha256": "f37c11784076adc9ed319e552ddedab1c6984a39ff90b765402ab120bc5690f3", "sha256_in_prefix": "f37c11784076adc9ed319e552ddedab1c6984a39ff90b765402ab120bc5690f3", "size_in_bytes": 1461}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/setuptools_build.py", "path_type": "hardlink", "sha256": "d112b82b4eeaa5a0609d89b9d3f7f977955d79d60f9357e5f5034a68e6bad173", "sha256_in_prefix": "d112b82b4eeaa5a0609d89b9d3f7f977955d79d60f9357e5f5034a68e6bad173", "size_in_bytes": 4499}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/subprocess.py", "path_type": "hardlink", "sha256": "af8f816bf61cdee6574298b42b8a5916c093fd0a9d4afb45dd727edb4e1059a0", "sha256_in_prefix": "af8f816bf61cdee6574298b42b8a5916c093fd0a9d4afb45dd727edb4e1059a0", "size_in_bytes": 8983}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/temp_dir.py", "path_type": "hardlink", "sha256": "0fd73c0fb58e3eba0e3bc1860eaa417954a3d74346166ba71b4a364e87638c85", "sha256_in_prefix": "0fd73c0fb58e3eba0e3bc1860eaa417954a3d74346166ba71b4a364e87638c85", "size_in_bytes": 9307}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/unpacking.py", "path_type": "hardlink", "sha256": "e2d6360feb1bb41b762dcc2cf7c126dcc3c03dd1e75d20579e9494d0ba065194", "sha256_in_prefix": "e2d6360feb1bb41b762dcc2cf7c126dcc3c03dd1e75d20579e9494d0ba065194", "size_in_bytes": 11939}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/urls.py", "path_type": "hardlink", "sha256": "685fde83dba5e5df1b302c5f4924b141c7ecf8ea4975b4ad62a647a32d8ad511", "sha256_in_prefix": "685fde83dba5e5df1b302c5f4924b141c7ecf8ea4975b4ad62a647a32d8ad511", "size_in_bytes": 1601}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/virtualenv.py", "path_type": "hardlink", "sha256": "997f943f2c3530fc61c14c4a85ba96597ef427a3c75c0263555ad19c6d21f667", "sha256_in_prefix": "997f943f2c3530fc61c14c4a85ba96597ef427a3c75c0263555ad19c6d21f667", "size_in_bytes": 3455}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/wheel.py", "path_type": "hardlink", "sha256": "61d46e8fa322706f90f4c834dc56d4bf55932da9ba2dcec08228d8e2fa15ca2b", "sha256_in_prefix": "61d46e8fa322706f90f4c834dc56d4bf55932da9ba2dcec08228d8e2fa15ca2b", "size_in_bytes": 4468}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/__init__.py", "path_type": "hardlink", "sha256": "500aafce96e2d156d9a3751beac904799030fa8a08651fb35ff5a909bc720a85", "sha256_in_prefix": "500aafce96e2d156d9a3751beac904799030fa8a08651fb35ff5a909bc720a85", "size_in_bytes": 596}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/bazaar.py", "path_type": "hardlink", "sha256": "dd6d5e1e3918c76bdce9ba1e6f634187823fae51805ccfafaf37cd84b9b54718", "sha256_in_prefix": "dd6d5e1e3918c76bdce9ba1e6f634187823fae51805ccfafaf37cd84b9b54718", "size_in_bytes": 3734}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/git.py", "path_type": "hardlink", "sha256": "4d37aa0eecd2fbf04548db944ad556984da7183a4abaf521041264fc20905d5d", "sha256_in_prefix": "4d37aa0eecd2fbf04548db944ad556984da7183a4abaf521041264fc20905d5d", "size_in_bytes": 19144}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/mercurial.py", "path_type": "hardlink", "sha256": "c3564958b2aa34fd689c48e47e39700559cca8f64d488111f1aca341c9d3ab8c", "sha256_in_prefix": "c3564958b2aa34fd689c48e47e39700559cca8f64d488111f1aca341c9d3ab8c", "size_in_bytes": 5575}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/subversion.py", "path_type": "hardlink", "sha256": "b9481d3efc66bc407c430b63af41dcd178058db88d8b971bbc8e2f0112ce257a", "sha256_in_prefix": "b9481d3efc66bc407c430b63af41dcd178058db88d8b971bbc8e2f0112ce257a", "size_in_bytes": 11787}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/versioncontrol.py", "path_type": "hardlink", "sha256": "77ebf599c2f1a1f83615a22a06b57e7be65c8ce810852d28c57a648b5bf5c97b", "sha256_in_prefix": "77ebf599c2f1a1f83615a22a06b57e7be65c8ce810852d28c57a648b5bf5c97b", "size_in_bytes": 22502}, {"_path": "lib/python3.10/site-packages/pip/_internal/wheel_builder.py", "path_type": "hardlink", "sha256": "3c3176c3aa7179e739b243f780b22b47f56b26b00ee291d1a549e435b27c4794", "sha256_in_prefix": "3c3176c3aa7179e739b243f780b22b47f56b26b00ee291d1a549e435b27c4794", "size_in_bytes": 11225}, {"_path": "lib/python3.10/site-packages/pip/_vendor/__init__.py", "path_type": "hardlink", "sha256": "5b3bac3d319620c790416495274876ada7c69154daf56289d874fedbe12866b5", "sha256_in_prefix": "5b3bac3d319620c790416495274876ada7c69154daf56289d874fedbe12866b5", "size_in_bytes": 4907}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__init__.py", "path_type": "hardlink", "sha256": "045da7e4e790cf5416db149ecb62f17cd0adc1b8e74da7577481f6b68a8979c8", "sha256_in_prefix": "045da7e4e790cf5416db149ecb62f17cd0adc1b8e74da7577481f6b68a8979c8", "size_in_bytes": 677}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/_cmd.py", "path_type": "hardlink", "sha256": "8a2b2dd84a7326f0d5221300c57abc8859d306c89901dea2a65c5f98d6e83729", "sha256_in_prefix": "8a2b2dd84a7326f0d5221300c57abc8859d306c89901dea2a65c5f98d6e83729", "size_in_bytes": 1737}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/adapter.py", "path_type": "hardlink", "sha256": "f32eab4cf5cecd51e60ca096e4247db22bcb56e0eff9ca5d19c65875158d68fc", "sha256_in_prefix": "f32eab4cf5cecd51e60ca096e4247db22bcb56e0eff9ca5d19c65875158d68fc", "size_in_bytes": 6599}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/cache.py", "path_type": "hardlink", "sha256": "397c2fec59f60309ca3626a12479e3b6f68a2e776f54bbfffb33be96d955f6a2", "sha256_in_prefix": "397c2fec59f60309ca3626a12479e3b6f68a2e776f54bbfffb33be96d955f6a2", "size_in_bytes": 1953}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/__init__.py", "path_type": "hardlink", "sha256": "76daebae82b90670034751968c2675f5a674b45b0c7ef141b4b410535b29fda8", "sha256_in_prefix": "76daebae82b90670034751968c2675f5a674b45b0c7ef141b4b410535b29fda8", "size_in_bytes": 303}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/file_cache.py", "path_type": "hardlink", "sha256": "77cba9166cbfcf06829a56d61150652d715d76df19c3c739485a7178e66c75fc", "sha256_in_prefix": "77cba9166cbfcf06829a56d61150652d715d76df19c3c739485a7178e66c75fc", "size_in_bytes": 4117}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/redis_cache.py", "path_type": "hardlink", "sha256": "f6b9aac2d62efe58d5916ebfa0ba9b0bb11a5ff6bc613ff22ee9daf9e4b4760a", "sha256_in_prefix": "f6b9aac2d62efe58d5916ebfa0ba9b0bb11a5ff6bc613ff22ee9daf9e4b4760a", "size_in_bytes": 1386}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/controller.py", "path_type": "hardlink", "sha256": "731d0797cc4b66052e5eecbbf068a1f406230adaaeae6623549c72038c96b7bc", "sha256_in_prefix": "731d0797cc4b66052e5eecbbf068a1f406230adaaeae6623549c72038c96b7bc", "size_in_bytes": 19101}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/filewrapper.py", "path_type": "hardlink", "sha256": "da4b5734f1342aa9f2cc5db868eb0a080e7c1d0ab5c5e0ba97683aff3c238217", "sha256_in_prefix": "da4b5734f1342aa9f2cc5db868eb0a080e7c1d0ab5c5e0ba97683aff3c238217", "size_in_bytes": 4291}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/heuristics.py", "path_type": "hardlink", "sha256": "82a31753cc34810b8442249dbb7620fb4bddf645bb9eb58a6cb71aef9ae17861", "sha256_in_prefix": "82a31753cc34810b8442249dbb7620fb4bddf645bb9eb58a6cb71aef9ae17861", "size_in_bytes": 4881}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/serialize.py", "path_type": "hardlink", "sha256": "1d0776225950d391f33e454b3174c5dae5f99a31108c3064c42a94254383a599", "sha256_in_prefix": "1d0776225950d391f33e454b3174c5dae5f99a31108c3064c42a94254383a599", "size_in_bytes": 5163}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/wrapper.py", "path_type": "hardlink", "sha256": "86c19cee0f101904d3fb87fcb60cf700ce6ac12720e853b405274b491744be95", "sha256_in_prefix": "86c19cee0f101904d3fb87fcb60cf700ce6ac12720e853b405274b491744be95", "size_in_bytes": 1417}, {"_path": "lib/python3.10/site-packages/pip/_vendor/certifi/__init__.py", "path_type": "hardlink", "sha256": "76f34351d017a7e873a1871fd3d3d7ccb9872253dfca968140f169e5eb035f2a", "sha256_in_prefix": "76f34351d017a7e873a1871fd3d3d7ccb9872253dfca968140f169e5eb035f2a", "size_in_bytes": 94}, {"_path": "lib/python3.10/site-packages/pip/_vendor/certifi/__main__.py", "path_type": "hardlink", "sha256": "d64dc2afde6f0b1c464460e58eb5b7c0c76965d2f73617f4bb59fe936a9db026", "sha256_in_prefix": "d64dc2afde6f0b1c464460e58eb5b7c0c76965d2f73617f4bb59fe936a9db026", "size_in_bytes": 255}, {"_path": "lib/python3.10/site-packages/pip/_vendor/certifi/cacert.pem", "path_type": "hardlink", "sha256": "966ddc609c44bfd4a8031e00b5cdcd893d43f7677deb9bdc203e87dfd7fff77a", "sha256_in_prefix": "966ddc609c44bfd4a8031e00b5cdcd893d43f7677deb9bdc203e87dfd7fff77a", "size_in_bytes": 290057}, {"_path": "lib/python3.10/site-packages/pip/_vendor/certifi/core.py", "path_type": "hardlink", "sha256": "82efc40952359b746ad32b69b0d13ad6181019c29a380b7d46cf46f0ab1308e2", "sha256_in_prefix": "82efc40952359b746ad32b69b0d13ad6181019c29a380b7d46cf46f0ab1308e2", "size_in_bytes": 3442}, {"_path": "lib/python3.10/site-packages/pip/_vendor/certifi/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__init__.py", "path_type": "hardlink", "sha256": "0b7385bb4346c03cd0e0b3a69923853ec452be46c19fe99d7788ffe58a89c3eb", "sha256_in_prefix": "0b7385bb4346c03cd0e0b3a69923853ec452be46c19fe99d7788ffe58a89c3eb", "size_in_bytes": 250}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__main__.py", "path_type": "hardlink", "sha256": "50d4ccecfe667d5b53ef00e2f643935d6815ddfbb77bc6d3aedd50a758ef8caa", "sha256_in_prefix": "50d4ccecfe667d5b53ef00e2f643935d6815ddfbb77bc6d3aedd50a758ef8caa", "size_in_bytes": 1709}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/_implementation.py", "path_type": "hardlink", "sha256": "1aa6f60e54042d16a478794a7fa42d4125b433e6dc1289b11f0e09b2f203d65b", "sha256_in_prefix": "1aa6f60e54042d16a478794a7fa42d4125b433e6dc1289b11f0e09b2f203d65b", "size_in_bytes": 8041}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/_lint_dependency_groups.py", "path_type": "hardlink", "sha256": "ca9f830ea297b5b9034cd6b489f6be166380f2b6b6e253d91177ed5be47902e2", "sha256_in_prefix": "ca9f830ea297b5b9034cd6b489f6be166380f2b6b6e253d91177ed5be47902e2", "size_in_bytes": 1710}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/_pip_wrapper.py", "path_type": "hardlink", "sha256": "9ee556ff0fe7b55c69136e842c4be780c634374e2c14bb228d747265944e146f", "sha256_in_prefix": "9ee556ff0fe7b55c69136e842c4be780c634374e2c14bb228d747265944e146f", "size_in_bytes": 1865}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/_toml_compat.py", "path_type": "hardlink", "sha256": "0479d79c569c9b70dea25b00df91a323aaa40c0a6fb9afb5176d24bf705f6561", "sha256_in_prefix": "0479d79c569c9b70dea25b00df91a323aaa40c0a6fb9afb5176d24bf705f6561", "size_in_bytes": 285}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/__init__.py", "path_type": "hardlink", "sha256": "0dea37ba8f7c694c887dd28936aa1f7921055b00f3ad5d907862d72ec82ad008", "sha256_in_prefix": "0dea37ba8f7c694c887dd28936aa1f7921055b00f3ad5d907862d72ec82ad008", "size_in_bytes": 625}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/compat.py", "path_type": "hardlink", "sha256": "da34528d1238a3ebe55de4cad8108621486473a7bd646852b1a711339a2c793c", "sha256_in_prefix": "da34528d1238a3ebe55de4cad8108621486473a7bd646852b1a711339a2c793c", "size_in_bytes": 41467}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/resources.py", "path_type": "hardlink", "sha256": "2f06cf92c73403524c6e2e979ee3dd301527f375fb04fb85356a8f184288ebdf", "sha256_in_prefix": "2f06cf92c73403524c6e2e979ee3dd301527f375fb04fb85356a8f184288ebdf", "size_in_bytes": 10820}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/scripts.py", "path_type": "hardlink", "sha256": "42fa7be84f49737220c98b9b9e9a88f5f4bb7ac78639ee058e97952aa2adf48c", "sha256_in_prefix": "42fa7be84f49737220c98b9b9e9a88f5f4bb7ac78639ee058e97952aa2adf48c", "size_in_bytes": 18612}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/t32.exe", "path_type": "hardlink", "sha256": "6b4195e640a85ac32eb6f9628822a622057df1e459df7c17a12f97aeabc9415b", "sha256_in_prefix": "6b4195e640a85ac32eb6f9628822a622057df1e459df7c17a12f97aeabc9415b", "size_in_bytes": 97792}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/t64-arm.exe", "path_type": "hardlink", "sha256": "ebc4c06b7d95e74e315419ee7e88e1d0f71e9e9477538c00a93a9ff8c66a6cfc", "sha256_in_prefix": "ebc4c06b7d95e74e315419ee7e88e1d0f71e9e9477538c00a93a9ff8c66a6cfc", "size_in_bytes": 182784}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/t64.exe", "path_type": "hardlink", "sha256": "81a618f21cb87db9076134e70388b6e9cb7c2106739011b6a51772d22cae06b7", "sha256_in_prefix": "81a618f21cb87db9076134e70388b6e9cb7c2106739011b6a51772d22cae06b7", "size_in_bytes": 108032}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/util.py", "path_type": "hardlink", "sha256": "bcc3c6bec4b88fd845e98f64dd3ca89b569a1cb6f4ac5999004cb378075e97dc", "sha256_in_prefix": "bcc3c6bec4b88fd845e98f64dd3ca89b569a1cb6f4ac5999004cb378075e97dc", "size_in_bytes": 66682}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/w32.exe", "path_type": "hardlink", "sha256": "47872cc77f8e18cf642f868f23340a468e537e64521d9a3a416c8b84384d064b", "sha256_in_prefix": "47872cc77f8e18cf642f868f23340a468e537e64521d9a3a416c8b84384d064b", "size_in_bytes": 91648}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/w64-arm.exe", "path_type": "hardlink", "sha256": "c5dc9884a8f458371550e09bd396e5418bf375820a31b9899f6499bf391c7b2e", "sha256_in_prefix": "c5dc9884a8f458371550e09bd396e5418bf375820a31b9899f6499bf391c7b2e", "size_in_bytes": 168448}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/w64.exe", "path_type": "hardlink", "sha256": "7a319ffaba23a017d7b1e18ba726ba6c54c53d6446db55f92af53c279894f8ad", "sha256_in_prefix": "7a319ffaba23a017d7b1e18ba726ba6c54c53d6446db55f92af53c279894f8ad", "size_in_bytes": 101888}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distro/__init__.py", "path_type": "hardlink", "sha256": "d9f1e317e49f80fbe3c8d67588787fc23a96751fd8a393831f0642d232c13e17", "sha256_in_prefix": "d9f1e317e49f80fbe3c8d67588787fc23a96751fd8a393831f0642d232c13e17", "size_in_bytes": 981}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distro/__main__.py", "path_type": "hardlink", "sha256": "6eef5ddd389fa0a72264572a441bb2815dc64ae4e19d50ff9b620ae1ccfde95b", "sha256_in_prefix": "6eef5ddd389fa0a72264572a441bb2815dc64ae4e19d50ff9b620ae1ccfde95b", "size_in_bytes": 64}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distro/distro.py", "path_type": "hardlink", "sha256": "5ea6de7da7008434f8cebfedae76c0d79798f2f74ae064e08609af506ac433fe", "sha256_in_prefix": "5ea6de7da7008434f8cebfedae76c0d79798f2f74ae064e08609af506ac433fe", "size_in_bytes": 49430}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distro/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/__init__.py", "path_type": "hardlink", "sha256": "30fa8d0cb65b5ea19a35d5f1005862a853ca1105e3bb68cd42109ecbafb97893", "sha256_in_prefix": "30fa8d0cb65b5ea19a35d5f1005862a853ca1105e3bb68cd42109ecbafb97893", "size_in_bytes": 868}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/codec.py", "path_type": "hardlink", "sha256": "3c47b0dc8b70ce35b887299b6ac9edcb6376397bcd7201c1f898eb06ec473d86", "sha256_in_prefix": "3c47b0dc8b70ce35b887299b6ac9edcb6376397bcd7201c1f898eb06ec473d86", "size_in_bytes": 3422}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/compat.py", "path_type": "hardlink", "sha256": "4732f2e90402765f7bf3868585bd845fd10a1822638343f73e294675e5d7731f", "sha256_in_prefix": "4732f2e90402765f7bf3868585bd845fd10a1822638343f73e294675e5d7731f", "size_in_bytes": 316}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/core.py", "path_type": "hardlink", "sha256": "60963200c9f089010f8d50b8f85aaefe9e0227ac8a2ae0c69a9a41350350a45b", "sha256_in_prefix": "60963200c9f089010f8d50b8f85aaefe9e0227ac8a2ae0c69a9a41350350a45b", "size_in_bytes": 13239}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/idnadata.py", "path_type": "hardlink", "sha256": "5b7d067081afb4e598c008d98f8663ba8b94bad0ba7df80dbb28c9cbb7d9fa5a", "sha256_in_prefix": "5b7d067081afb4e598c008d98f8663ba8b94bad0ba7df80dbb28c9cbb7d9fa5a", "size_in_bytes": 78306}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/intranges.py", "path_type": "hardlink", "sha256": "6a652d91d8587101bc66bf82a0c33f91545a731922bc2d568313756fadca29d5", "sha256_in_prefix": "6a652d91d8587101bc66bf82a0c33f91545a731922bc2d568313756fadca29d5", "size_in_bytes": 1898}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/package_data.py", "path_type": "hardlink", "sha256": "ab9f52dce5ec739548f23eaf483d2c18133293acd9e2f58544413cf3208960ab", "sha256_in_prefix": "ab9f52dce5ec739548f23eaf483d2c18133293acd9e2f58544413cf3208960ab", "size_in_bytes": 21}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/uts46data.py", "path_type": "hardlink", "sha256": "aedf742bd278d20512c29a433c2ae18e08b9000ea958ceb974419149feab2213", "sha256_in_prefix": "aedf742bd278d20512c29a433c2ae18e08b9000ea958ceb974419149feab2213", "size_in_bytes": 239289}, {"_path": "lib/python3.10/site-packages/pip/_vendor/msgpack/__init__.py", "path_type": "hardlink", "sha256": "ab64f93d1b084d5a5ecad812d16892aa893d218f15fda1620bc5559574c24e00", "sha256_in_prefix": "ab64f93d1b084d5a5ecad812d16892aa893d218f15fda1620bc5559574c24e00", "size_in_bytes": 1109}, {"_path": "lib/python3.10/site-packages/pip/_vendor/msgpack/exceptions.py", "path_type": "hardlink", "sha256": "7424d67a2f1da64accb100dc8d093be004e5f47b08047d326edf3338f36a3187", "sha256_in_prefix": "7424d67a2f1da64accb100dc8d093be004e5f47b08047d326edf3338f36a3187", "size_in_bytes": 1081}, {"_path": "lib/python3.10/site-packages/pip/_vendor/msgpack/ext.py", "path_type": "hardlink", "sha256": "92d789bf4de7f6d633779a28df1628a554e8e2f45a031a27050409857a21659a", "sha256_in_prefix": "92d789bf4de7f6d633779a28df1628a554e8e2f45a031a27050409857a21659a", "size_in_bytes": 5726}, {"_path": "lib/python3.10/site-packages/pip/_vendor/msgpack/fallback.py", "path_type": "hardlink", "sha256": "d20d4fce9d2fb66044989e70f45decffe24c55444ff114b81b571ce5345a02c2", "sha256_in_prefix": "d20d4fce9d2fb66044989e70f45decffe24c55444ff114b81b571ce5345a02c2", "size_in_bytes": 32390}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__init__.py", "path_type": "hardlink", "sha256": "ff470388f55fd92f9b35f566660bb1c739ab2185a5c804b1a6aa61e2ab095947", "sha256_in_prefix": "ff470388f55fd92f9b35f566660bb1c739ab2185a5c804b1a6aa61e2ab095947", "size_in_bytes": 494}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/_elffile.py", "path_type": "hardlink", "sha256": "524adb0ed5bb69eab7aaaa007d4d7aa23c87675c6c4ef1a47bf5aa31328029dd", "sha256_in_prefix": "524adb0ed5bb69eab7aaaa007d4d7aa23c87675c6c4ef1a47bf5aa31328029dd", "size_in_bytes": 3286}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "b78cbff9d4ce71faf7ea02d8fb3b623a9c7124518eda2902d751e07f2b06c623", "sha256_in_prefix": "b78cbff9d4ce71faf7ea02d8fb3b623a9c7124518eda2902d751e07f2b06c623", "size_in_bytes": 9596}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "sha256_in_prefix": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "size_in_bytes": 2694}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/_parser.py", "path_type": "hardlink", "sha256": "8187e78f4a511df955738447662b75dca353c8df62895714d915021a2db60703", "sha256_in_prefix": "8187e78f4a511df955738447662b75dca353c8df62895714d915021a2db60703", "size_in_bytes": 10221}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "398cedeea2b1ca538027eab45f22b5a80c9cc8f4582df30f74640a4579195b22", "sha256_in_prefix": "398cedeea2b1ca538027eab45f22b5a80c9cc8f4582df30f74640a4579195b22", "size_in_bytes": 5310}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/licenses/__init__.py", "path_type": "hardlink", "sha256": "ddbc7e82bca8e2c46fe4bb2bc00a68bb2eb9548b37bba8ab48e449cc02e4af35", "sha256_in_prefix": "ddbc7e82bca8e2c46fe4bb2bc00a68bb2eb9548b37bba8ab48e449cc02e4af35", "size_in_bytes": 5727}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/licenses/_spdx.py", "path_type": "hardlink", "sha256": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "sha256_in_prefix": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "size_in_bytes": 48398}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/markers.py", "path_type": "hardlink", "sha256": "3f4c1edbb8e6d71533806309c418ed50508859e0714ec31e253389e9c8599806", "sha256_in_prefix": "3f4c1edbb8e6d71533806309c418ed50508859e0714ec31e253389e9c8599806", "size_in_bytes": 12049}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/metadata.py", "path_type": "hardlink", "sha256": "f08644aea4109cd9b9ddd659b98ab8146538fdda728a731e1f540504858891f1", "sha256_in_prefix": "f08644aea4109cd9b9ddd659b98ab8146538fdda728a731e1f540504858891f1", "size_in_bytes": 34739}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/requirements.py", "path_type": "hardlink", "sha256": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "sha256_in_prefix": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "size_in_bytes": 2947}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/specifiers.py", "path_type": "hardlink", "sha256": "c9cf43fccc9c2449b052966f72cd4e64bf477e23459b2c3445969e1d134790fc", "sha256_in_prefix": "c9cf43fccc9c2449b052966f72cd4e64bf477e23459b2c3445969e1d134790fc", "size_in_bytes": 40079}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/tags.py", "path_type": "hardlink", "sha256": "e35b3ded6f596adaead8477b45cdea7810da1def2928abd8ab69868f06a17d79", "sha256_in_prefix": "e35b3ded6f596adaead8477b45cdea7810da1def2928abd8ab69868f06a17d79", "size_in_bytes": 22745}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/utils.py", "path_type": "hardlink", "sha256": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "sha256_in_prefix": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "size_in_bytes": 5050}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/version.py", "path_type": "hardlink", "sha256": "a221eacd352ffe9d768698e0b0b0d571a179853ee90da48e56250d303e064d6d", "sha256_in_prefix": "a221eacd352ffe9d768698e0b0b0d571a179853ee90da48e56250d303e064d6d", "size_in_bytes": 16688}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pkg_resources/__init__.py", "path_type": "hardlink", "sha256": "bdb4c9d3faee5201b1423944aacaee16688d3d5ca1dadf6afb3c930d3d39df12", "sha256_in_prefix": "bdb4c9d3faee5201b1423944aacaee16688d3d5ca1dadf6afb3c930d3d39df12", "size_in_bytes": 124451}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/__init__.py", "path_type": "hardlink", "sha256": "51f7921d697c01e4ed6ce04ea070312b874e0ce5a466d7f2fa6fe2edc59d27c7", "sha256_in_prefix": "51f7921d697c01e4ed6ce04ea070312b874e0ce5a466d7f2fa6fe2edc59d27c7", "size_in_bytes": 22344}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/__main__.py", "path_type": "hardlink", "sha256": "8c127ccdbecca71e5e6dca85f37c6ba4ef7831a782a4d18755ff5cbc337624b8", "sha256_in_prefix": "8c127ccdbecca71e5e6dca85f37c6ba4ef7831a782a4d18755ff5cbc337624b8", "size_in_bytes": 1505}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/android.py", "path_type": "hardlink", "sha256": "af40ec85505ff913b58d72465fc0b84e297b1755d6b7e6e47563209af1226988", "sha256_in_prefix": "af40ec85505ff913b58d72465fc0b84e297b1755d6b7e6e47563209af1226988", "size_in_bytes": 9013}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/api.py", "path_type": "hardlink", "sha256": "53d133237118c5c5d6502b48191965aab70df7d8b62d26359aadbe1adb14c044", "sha256_in_prefix": "53d133237118c5c5d6502b48191965aab70df7d8b62d26359aadbe1adb14c044", "size_in_bytes": 9277}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/macos.py", "path_type": "hardlink", "sha256": "5256f2159f11ceedf19dd0aa4041eb7ec613787c187456a9d48a33fb2c6f793e", "sha256_in_prefix": "5256f2159f11ceedf19dd0aa4041eb7ec613787c187456a9d48a33fb2c6f793e", "size_in_bytes": 6154}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/unix.py", "path_type": "hardlink", "sha256": "5999a4500fbe2f724d4469b3df6b37e587e80f789c6bac4a20f74257f1e12dcb", "sha256_in_prefix": "5999a4500fbe2f724d4469b3df6b37e587e80f789c6bac4a20f74257f1e12dcb", "size_in_bytes": 10458}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/version.py", "path_type": "hardlink", "sha256": "75d37711c50f7deafb09baa6c85366834dd1deefaa0e7df64ff7cbb31db9f829", "sha256_in_prefix": "75d37711c50f7deafb09baa6c85366834dd1deefaa0e7df64ff7cbb31db9f829", "size_in_bytes": 511}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/windows.py", "path_type": "hardlink", "sha256": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "sha256_in_prefix": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "size_in_bytes": 10125}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__init__.py", "path_type": "hardlink", "sha256": "f2e36a2420b05ea6c4c79692b01af417291440e0432818a13b998faa2c356aaa", "sha256_in_prefix": "f2e36a2420b05ea6c4c79692b01af417291440e0432818a13b998faa2c356aaa", "size_in_bytes": 2983}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__main__.py", "path_type": "hardlink", "sha256": "5ab9dda527ba8b57245ff490d4a6b10fd093286cc3d04b82385c5c6f0169a0b5", "sha256_in_prefix": "5ab9dda527ba8b57245ff490d4a6b10fd093286cc3d04b82385c5c6f0169a0b5", "size_in_bytes": 353}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/console.py", "path_type": "hardlink", "sha256": "01a8035aac1e6b6c8159fd74282f69b4180ca4c8f12a9f3200102687e3503959", "sha256_in_prefix": "01a8035aac1e6b6c8159fd74282f69b4180ca4c8f12a9f3200102687e3503959", "size_in_bytes": 1718}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/filter.py", "path_type": "hardlink", "sha256": "60bb694e7662bb4ee7637a0af677d1e84f58d4504784fe4f5fc82f90959c7da8", "sha256_in_prefix": "60bb694e7662bb4ee7637a0af677d1e84f58d4504784fe4f5fc82f90959c7da8", "size_in_bytes": 1910}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/filters/__init__.py", "path_type": "hardlink", "sha256": "e14e23b40d17de23fcdee42707df9323e1c34b0f04f32f333181dad148db6da2", "sha256_in_prefix": "e14e23b40d17de23fcdee42707df9323e1c34b0f04f32f333181dad148db6da2", "size_in_bytes": 40392}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/formatter.py", "path_type": "hardlink", "sha256": "29940c9b2a3fc643889101bc83ae8b6049018756f1edad07c8608172f848f44c", "sha256_in_prefix": "29940c9b2a3fc643889101bc83ae8b6049018756f1edad07c8608172f848f44c", "size_in_bytes": 4390}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/formatters/__init__.py", "path_type": "hardlink", "sha256": "293c019a75e56a8a498500ce7a6547607b24883821baafb4f18b4feb13cd24f8", "sha256_in_prefix": "293c019a75e56a8a498500ce7a6547607b24883821baafb4f18b4feb13cd24f8", "size_in_bytes": 5385}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/formatters/_mapping.py", "path_type": "hardlink", "sha256": "d42c37ec5b9094d69c9f144a9ad94f5f89f22e85fdfedb64a39670b1c354659e", "sha256_in_prefix": "d42c37ec5b9094d69c9f144a9ad94f5f89f22e85fdfedb64a39670b1c354659e", "size_in_bytes": 4176}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/lexer.py", "path_type": "hardlink", "sha256": "fe406b389fcd4f94e5d0854cd2b03d73c7b2b0febfcab946cc4408d1e55807e0", "sha256_in_prefix": "fe406b389fcd4f94e5d0854cd2b03d73c7b2b0febfcab946cc4408d1e55807e0", "size_in_bytes": 35349}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/__init__.py", "path_type": "hardlink", "sha256": "c1b20c137e461fb6c8d41f6b34f245a964fe8a3fd102964360f52567271a2f30", "sha256_in_prefix": "c1b20c137e461fb6c8d41f6b34f245a964fe8a3fd102964360f52567271a2f30", "size_in_bytes": 12115}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/_mapping.py", "path_type": "hardlink", "sha256": "978b425ccf1ef5a3c2d810fab2322bd1d793f89fb3e6d1e00b02fea757d2d0f1", "sha256_in_prefix": "978b425ccf1ef5a3c2d810fab2322bd1d793f89fb3e6d1e00b02fea757d2d0f1", "size_in_bytes": 77602}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/python.py", "path_type": "hardlink", "sha256": "bf18e7d5c38772520a24ac68ca206c41363ae461db919b5946e290d8054229ac", "sha256_in_prefix": "bf18e7d5c38772520a24ac68ca206c41363ae461db919b5946e290d8054229ac", "size_in_bytes": 53853}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/modeline.py", "path_type": "hardlink", "sha256": "2b9792911f064b5af93a45d74c739c57468cffac6993d7963442005be38e2768", "sha256_in_prefix": "2b9792911f064b5af93a45d74c739c57468cffac6993d7963442005be38e2768", "size_in_bytes": 1005}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/plugin.py", "path_type": "hardlink", "sha256": "b4fc74ac9093219f62a1180b3581b8a627c26c0c1345465d76f2f0f8d7c0936c", "sha256_in_prefix": "b4fc74ac9093219f62a1180b3581b8a627c26c0c1345465d76f2f0f8d7c0936c", "size_in_bytes": 1891}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/regexopt.py", "path_type": "hardlink", "sha256": "c1768ff468e9fe1280767202aa80e447100e40949ce2fdd7ea6731c77cfe4cdb", "sha256_in_prefix": "c1768ff468e9fe1280767202aa80e447100e40949ce2fdd7ea6731c77cfe4cdb", "size_in_bytes": 3072}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/scanner.py", "path_type": "hardlink", "sha256": "9cd7044d1475b51ba24da1e61d24d310255814f70b7fa98366ed5ee2ef7503d1", "sha256_in_prefix": "9cd7044d1475b51ba24da1e61d24d310255814f70b7fa98366ed5ee2ef7503d1", "size_in_bytes": 3092}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/sphinxext.py", "path_type": "hardlink", "sha256": "e71ed987d62553a212277d5d33076e89a6a76f97566672a0dccc8442cb1e3674", "sha256_in_prefix": "e71ed987d62553a212277d5d33076e89a6a76f97566672a0dccc8442cb1e3674", "size_in_bytes": 7981}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/style.py", "path_type": "hardlink", "sha256": "3e5399aa5b274d5779f111b2e74be403671743f94fe5b1791063040539e8e830", "sha256_in_prefix": "3e5399aa5b274d5779f111b2e74be403671743f94fe5b1791063040539e8e830", "size_in_bytes": 6420}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/styles/__init__.py", "path_type": "hardlink", "sha256": "c7d79b72d7f2bc2005a4c4e5309e58c7070d601ce382aeb325a2a4366efcaf83", "sha256_in_prefix": "c7d79b72d7f2bc2005a4c4e5309e58c7070d601ce382aeb325a2a4366efcaf83", "size_in_bytes": 2042}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/styles/_mapping.py", "path_type": "hardlink", "sha256": "ea5a2f154136f6dcfa12c5775d8638860a3327bab524bedc7cedd43a58274bcc", "sha256_in_prefix": "ea5a2f154136f6dcfa12c5775d8638860a3327bab524bedc7cedd43a58274bcc", "size_in_bytes": 3312}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/token.py", "path_type": "hardlink", "sha256": "59b7561a1626fd5a2c6f40c3c56f651cd3e02135df593b10987b7a732f516dc3", "sha256_in_prefix": "59b7561a1626fd5a2c6f40c3c56f651cd3e02135df593b10987b7a732f516dc3", "size_in_bytes": 6226}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/unistring.py", "path_type": "hardlink", "sha256": "6a5fbfac17a646e1af8a7b2b33a6ad36c1d3989e8351bc36e2ad8ed91bb57017", "sha256_in_prefix": "6a5fbfac17a646e1af8a7b2b33a6ad36c1d3989e8351bc36e2ad8ed91bb57017", "size_in_bytes": 63208}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/util.py", "path_type": "hardlink", "sha256": "a11b52a62028e6333dba59ed92f55b81751d8805b9ee39ee60607bb7d7d8ba17", "sha256_in_prefix": "a11b52a62028e6333dba59ed92f55b81751d8805b9ee39ee60607bb7d7d8ba17", "size_in_bytes": 10031}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/__init__.py", "path_type": "hardlink", "sha256": "70f07f6bd2d7cf9c6fb116d7d68daac807632dab5925d43f2dce4c70d5fe5fb6", "sha256_in_prefix": "70f07f6bd2d7cf9c6fb116d7d68daac807632dab5925d43f2dce4c70d5fe5fb6", "size_in_bytes": 691}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/_impl.py", "path_type": "hardlink", "sha256": "8d8fab6b19e6c91c81e7baee022b6b25153311ec6e021193a6033282ac7aed9e", "sha256_in_prefix": "8d8fab6b19e6c91c81e7baee022b6b25153311ec6e021193a6033282ac7aed9e", "size_in_bytes": 14936}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/_in_process/__init__.py", "path_type": "hardlink", "sha256": "30934fa5f23170ef85821c6905bc641b5ac58907fa1ce51b5785399aad07167b", "sha256_in_prefix": "30934fa5f23170ef85821c6905bc641b5ac58907fa1ce51b5785399aad07167b", "size_in_bytes": 557}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/_in_process/_in_process.py", "path_type": "hardlink", "sha256": "a9c5cc866c7ffcc209ab5d201875b7980e1397c772f18cc731c7309cda0a970d", "sha256_in_prefix": "a9c5cc866c7ffcc209ab5d201875b7980e1397c772f18cc731c7309cda0a970d", "size_in_bytes": 12216}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__init__.py", "path_type": "hardlink", "sha256": "1e507f1f386bcc6b5f0ff69a614c14875cd65cb67be7f6022f28adef9774573f", "sha256_in_prefix": "1e507f1f386bcc6b5f0ff69a614c14875cd65cb67be7f6022f28adef9774573f", "size_in_bytes": 5057}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__version__.py", "path_type": "hardlink", "sha256": "143abaf3563712f063743a7952aa65319dbcb934d894cfc989bd2c015f8da577", "sha256_in_prefix": "143abaf3563712f063743a7952aa65319dbcb934d894cfc989bd2c015f8da577", "size_in_bytes": 435}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/_internal_utils.py", "path_type": "hardlink", "sha256": "9cc4329abe21b37d93a95a3901b0ab99c24486f3d487bc57965bb2ab0b252e24", "sha256_in_prefix": "9cc4329abe21b37d93a95a3901b0ab99c24486f3d487bc57965bb2ab0b252e24", "size_in_bytes": 1495}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/adapters.py", "path_type": "hardlink", "sha256": "27b55e571281bdac1bb655f60c4455a34e49f415d371660b30735dd4169af9b9", "sha256_in_prefix": "27b55e571281bdac1bb655f60c4455a34e49f415d371660b30735dd4169af9b9", "size_in_bytes": 27607}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/api.py", "path_type": "hardlink", "sha256": "fd96fd39aeedcd5222cd32b016b3e30c463d7a3b66fce9d2444467003c46b10b", "sha256_in_prefix": "fd96fd39aeedcd5222cd32b016b3e30c463d7a3b66fce9d2444467003c46b10b", "size_in_bytes": 6449}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/auth.py", "path_type": "hardlink", "sha256": "905ef9b6a9cb72d67d31ffe19bd4d9223e1c4169cde6ec51cfca16b31e70991d", "sha256_in_prefix": "905ef9b6a9cb72d67d31ffe19bd4d9223e1c4169cde6ec51cfca16b31e70991d", "size_in_bytes": 10186}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/certs.py", "path_type": "hardlink", "sha256": "9070e590afdb7ae1d778c3dce63b5adb0825f2074a7945ade5fda74c356bbedf", "sha256_in_prefix": "9070e590afdb7ae1d778c3dce63b5adb0825f2074a7945ade5fda74c356bbedf", "size_in_bytes": 441}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/compat.py", "path_type": "hardlink", "sha256": "41f6e67531626738d21cc5e232b7788e809153a45a6ed43dcc870fa1568722eb", "sha256_in_prefix": "41f6e67531626738d21cc5e232b7788e809153a45a6ed43dcc870fa1568722eb", "size_in_bytes": 1822}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/cookies.py", "path_type": "hardlink", "sha256": "6cd8be8aa123e0d3d9d34fa86feac7bf392f39bccdde5129830de0ea9692dd7c", "sha256_in_prefix": "6cd8be8aa123e0d3d9d34fa86feac7bf392f39bccdde5129830de0ea9692dd7c", "size_in_bytes": 18590}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/exceptions.py", "path_type": "hardlink", "sha256": "0f5c2acd85a77b5992dab538ded3fd09e3751bb400cbb7aa2fda3582877a123c", "sha256_in_prefix": "0f5c2acd85a77b5992dab538ded3fd09e3751bb400cbb7aa2fda3582877a123c", "size_in_bytes": 4272}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/help.py", "path_type": "hardlink", "sha256": "85129a7fdbb41bb7ddc2ba8c1ed177a06d7a44a92d45fe8a8b0b52ab6168d7fd", "sha256_in_prefix": "85129a7fdbb41bb7ddc2ba8c1ed177a06d7a44a92d45fe8a8b0b52ab6168d7fd", "size_in_bytes": 3813}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/hooks.py", "path_type": "hardlink", "sha256": "0a2bb2b221c0dfd57951f702057148c7cdc8ac3a6ec1f37d45c4d482fdbc7ed4", "sha256_in_prefix": "0a2bb2b221c0dfd57951f702057148c7cdc8ac3a6ec1f37d45c4d482fdbc7ed4", "size_in_bytes": 733}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/models.py", "path_type": "hardlink", "sha256": "b5a963960eaf2786fec4cbb64da30d14591a8b031ec7f56c110eaa513377c336", "sha256_in_prefix": "b5a963960eaf2786fec4cbb64da30d14591a8b031ec7f56c110eaa513377c336", "size_in_bytes": 35575}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/packages.py", "path_type": "hardlink", "sha256": "fd94030894c9f123f79155ae9d2a81b1164d3f38f673558556a6ddaf4f29cf75", "sha256_in_prefix": "fd94030894c9f123f79155ae9d2a81b1164d3f38f673558556a6ddaf4f29cf75", "size_in_bytes": 1057}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/sessions.py", "path_type": "hardlink", "sha256": "ca44c8f145864a5b4e7c7d3b1caa25947ee44c11b0e168620556901a67244f0e", "sha256_in_prefix": "ca44c8f145864a5b4e7c7d3b1caa25947ee44c11b0e168620556901a67244f0e", "size_in_bytes": 30495}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/status_codes.py", "path_type": "hardlink", "sha256": "889500780db96da4ddc3ee8f7c3d1e178aa1a48343251248fb268cab1b382c42", "sha256_in_prefix": "889500780db96da4ddc3ee8f7c3d1e178aa1a48343251248fb268cab1b382c42", "size_in_bytes": 4322}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/structures.py", "path_type": "hardlink", "sha256": "f886e6855cf4e92fb968f499b94b6167afba0fd5ce8d1b935c739a6d8d38d573", "sha256_in_prefix": "f886e6855cf4e92fb968f499b94b6167afba0fd5ce8d1b935c739a6d8d38d573", "size_in_bytes": 2912}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/utils.py", "path_type": "hardlink", "sha256": "592df01d241a6847dc7aed6cca2168e637f87bf0962944dea7f21a6ce548fc9d", "sha256_in_prefix": "592df01d241a6847dc7aed6cca2168e637f87bf0962944dea7f21a6ce548fc9d", "size_in_bytes": 33225}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/__init__.py", "path_type": "hardlink", "sha256": "4f42dc02bf527da8ba6576a7a706af74711e6be027c364191b1d7acddde530a6", "sha256_in_prefix": "4f42dc02bf527da8ba6576a7a706af74711e6be027c364191b1d7acddde530a6", "size_in_bytes": 541}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/providers.py", "path_type": "hardlink", "sha256": "a485896c874927d185b4d6edc131f421ae37563ea16021098f60ce2ee7b5e453", "sha256_in_prefix": "a485896c874927d185b4d6edc131f421ae37563ea16021098f60ce2ee7b5e453", "size_in_bytes": 8914}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/reporters.py", "path_type": "hardlink", "sha256": "a4d25fe271712e901e2b1941522d86123d1ad888f59e2918d1469b4ca5deb13e", "sha256_in_prefix": "a4d25fe271712e901e2b1941522d86123d1ad888f59e2918d1469b4ca5deb13e", "size_in_bytes": 2037}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__init__.py", "path_type": "hardlink", "sha256": "ef6f0cdc4be69cf6d55d2ec4c5796fda432ee9bef012ca0fbad11f97eb9593f2", "sha256_in_prefix": "ef6f0cdc4be69cf6d55d2ec4c5796fda432ee9bef012ca0fbad11f97eb9593f2", "size_in_bytes": 640}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/abstract.py", "path_type": "hardlink", "sha256": "8d9381562804e0f52e6fd8b717e6d3bc1c1a2175fc4bd114dc2180481bc5a845", "sha256_in_prefix": "8d9381562804e0f52e6fd8b717e6d3bc1c1a2175fc4bd114dc2180481bc5a845", "size_in_bytes": 1558}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/criterion.py", "path_type": "hardlink", "sha256": "95c9991afe6c2873a7143fd1cd9c2f9464a3d7d31e03ee6b08ca5d7f64a0c3b6", "sha256_in_prefix": "95c9991afe6c2873a7143fd1cd9c2f9464a3d7d31e03ee6b08ca5d7f64a0c3b6", "size_in_bytes": 1768}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/exceptions.py", "path_type": "hardlink", "sha256": "967fe3690b602e545448563adbbca21c6da00fb020697cd129a12515587b7c34", "sha256_in_prefix": "967fe3690b602e545448563adbbca21c6da00fb020697cd129a12515587b7c34", "size_in_bytes": 1768}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/resolution.py", "path_type": "hardlink", "sha256": "a94eb854ab4df87c681729e69c8ea83fc68c3f36a2299b5bff5840487dc74c72", "sha256_in_prefix": "a94eb854ab4df87c681729e69c8ea83fc68c3f36a2299b5bff5840487dc74c72", "size_in_bytes": 23994}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/structs.py", "path_type": "hardlink", "sha256": "a6ef84262476201213af649078d3d16b4ad7863952b5f98efc6120021af7d34e", "sha256_in_prefix": "a6ef84262476201213af649078d3d16b4ad7863952b5f98efc6120021af7d34e", "size_in_bytes": 6420}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__init__.py", "path_type": "hardlink", "sha256": "751c6320bf926c5558d2adc88d232b7e00531eb9b52d90e02ceca0541c226197", "sha256_in_prefix": "751c6320bf926c5558d2adc88d232b7e00531eb9b52d90e02ceca0541c226197", "size_in_bytes": 6090}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__main__.py", "path_type": "hardlink", "sha256": "7bf6950beb43cdaad6416f52b9932e0a006be8e0d5fe20cd5765a1db19313a5c", "sha256_in_prefix": "7bf6950beb43cdaad6416f52b9932e0a006be8e0d5fe20cd5765a1db19313a5c", "size_in_bytes": 7896}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_cell_widths.py", "path_type": "hardlink", "sha256": "7db99ec9eb447478f313f571da5d6e2bbb673ce84cb365f59497cedefb0a0e90", "sha256_in_prefix": "7db99ec9eb447478f313f571da5d6e2bbb673ce84cb365f59497cedefb0a0e90", "size_in_bytes": 10209}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_emoji_codes.py", "path_type": "hardlink", "sha256": "86ed552fd9db55da6926b5688a356c85195c4517bfbf7763bb7326776b0a65d6", "sha256_in_prefix": "86ed552fd9db55da6926b5688a356c85195c4517bfbf7763bb7326776b0a65d6", "size_in_bytes": 140235}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_emoji_replace.py", "path_type": "hardlink", "sha256": "9fe91c7adb04531d99526850adf78c35cfad79e1a1a6e490e45f153c1b32bc3a", "sha256_in_prefix": "9fe91c7adb04531d99526850adf78c35cfad79e1a1a6e490e45f153c1b32bc3a", "size_in_bytes": 1064}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_export_format.py", "path_type": "hardlink", "sha256": "448d3ca52ae6e6d052ccf32f9db4ea6c3f5621a95a3a837977833545398bab56", "sha256_in_prefix": "448d3ca52ae6e6d052ccf32f9db4ea6c3f5621a95a3a837977833545398bab56", "size_in_bytes": 2128}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_extension.py", "path_type": "hardlink", "sha256": "5ede3b41a7022b062bbb38c38be80e06aef6e0945e0e3f429bdc548b97ebfb7e", "sha256_in_prefix": "5ede3b41a7022b062bbb38c38be80e06aef6e0945e0e3f429bdc548b97ebfb7e", "size_in_bytes": 265}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_fileno.py", "path_type": "hardlink", "sha256": "1d66713f90b66a331b1ebcaf01066c79f9557d0a06cec28e1f3286b0b0fcca74", "sha256_in_prefix": "1d66713f90b66a331b1ebcaf01066c79f9557d0a06cec28e1f3286b0b0fcca74", "size_in_bytes": 799}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_inspect.py", "path_type": "hardlink", "sha256": "44e4f43cb0b618c5a26a559992a2488e662aec83518a34109284a89164da0222", "sha256_in_prefix": "44e4f43cb0b618c5a26a559992a2488e662aec83518a34109284a89164da0222", "size_in_bytes": 9656}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_log_render.py", "path_type": "hardlink", "sha256": "d41c88d0f035669c5963708624e2b9e218e5ab85fe073fdba088c8a8277c2a7b", "sha256_in_prefix": "d41c88d0f035669c5963708624e2b9e218e5ab85fe073fdba088c8a8277c2a7b", "size_in_bytes": 3225}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_loop.py", "path_type": "hardlink", "sha256": "855ffa08b7683e6d2f6b6d96a70e332aa334458b33dd36715e3d0fa12fbd7834", "sha256_in_prefix": "855ffa08b7683e6d2f6b6d96a70e332aa334458b33dd36715e3d0fa12fbd7834", "size_in_bytes": 1236}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_null_file.py", "path_type": "hardlink", "sha256": "00318aa75cadfa4ef414c295ead9ea0aa79c07ead2273a7e590b03ecb3cbfa48", "sha256_in_prefix": "00318aa75cadfa4ef414c295ead9ea0aa79c07ead2273a7e590b03ecb3cbfa48", "size_in_bytes": 1394}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_palettes.py", "path_type": "hardlink", "sha256": "71d7afd4940a67426f960b95f62a478339d3767be52335050c16f422dd8fce32", "sha256_in_prefix": "71d7afd4940a67426f960b95f62a478339d3767be52335050c16f422dd8fce32", "size_in_bytes": 7063}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_pick.py", "path_type": "hardlink", "sha256": "7af0edf10378945e428b0ad421794e2429ed8ad0423ac23764b3c42005512c95", "sha256_in_prefix": "7af0edf10378945e428b0ad421794e2429ed8ad0423ac23764b3c42005512c95", "size_in_bytes": 423}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_ratio.py", "path_type": "hardlink", "sha256": "20eb65efcb1009866c987cb185ee3992b91bebcbbdc55cfbcc5b175490fe6f66", "sha256_in_prefix": "20eb65efcb1009866c987cb185ee3992b91bebcbbdc55cfbcc5b175490fe6f66", "size_in_bytes": 5325}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_spinners.py", "path_type": "hardlink", "sha256": "536af5fe0ff5cd28ec8e251d00449cda200c7378b8ae2fd2f0f60fea4439cf52", "sha256_in_prefix": "536af5fe0ff5cd28ec8e251d00449cda200c7378b8ae2fd2f0f60fea4439cf52", "size_in_bytes": 19919}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_stack.py", "path_type": "hardlink", "sha256": "f82f0e2bbaf19f7b0851d570c59041a5e1e12335f4788f9533731e9987da5e6d", "sha256_in_prefix": "f82f0e2bbaf19f7b0851d570c59041a5e1e12335f4788f9533731e9987da5e6d", "size_in_bytes": 351}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_timer.py", "path_type": "hardlink", "sha256": "cde9716d3ea83c566736bc163e973592d51e013f957387ee15c4592d018bb4c2", "sha256_in_prefix": "cde9716d3ea83c566736bc163e973592d51e013f957387ee15c4592d018bb4c2", "size_in_bytes": 417}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_win32_console.py", "path_type": "hardlink", "sha256": "05268344833004b2139ff9b499344b3ea304e6afaab8675232e60ca587982707", "sha256_in_prefix": "05268344833004b2139ff9b499344b3ea304e6afaab8675232e60ca587982707", "size_in_bytes": 22755}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_windows.py", "path_type": "hardlink", "sha256": "681c1a0ff4b9e926e0a2922f6b2566a64d18dbcbb06360b905a6f5c25dc1a7e2", "sha256_in_prefix": "681c1a0ff4b9e926e0a2922f6b2566a64d18dbcbb06360b905a6f5c25dc1a7e2", "size_in_bytes": 1925}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_windows_renderer.py", "path_type": "hardlink", "sha256": "b7be192f7c6e0c23f79e64e9f691f52f92e223671a909b9045095e1c225eae59", "sha256_in_prefix": "b7be192f7c6e0c23f79e64e9f691f52f92e223671a909b9045095e1c225eae59", "size_in_bytes": 2783}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/_wrap.py", "path_type": "hardlink", "sha256": "1654aca26e445f42d5900dca5b2df8c879c27cbb6a5fe6487a95ca87eef4ae97", "sha256_in_prefix": "1654aca26e445f42d5900dca5b2df8c879c27cbb6a5fe6487a95ca87eef4ae97", "size_in_bytes": 3404}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/abc.py", "path_type": "hardlink", "sha256": "38df84f99a924a1799f3c56b297d8cdcf5e915b18451464f31afc07f497ee1fd", "sha256_in_prefix": "38df84f99a924a1799f3c56b297d8cdcf5e915b18451464f31afc07f497ee1fd", "size_in_bytes": 890}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/align.py", "path_type": "hardlink", "sha256": "760fbbb98d2e90c2cb95412c04344bbdadb6fec4202090f804ad1d9991c51ee8", "sha256_in_prefix": "760fbbb98d2e90c2cb95412c04344bbdadb6fec4202090f804ad1d9991c51ee8", "size_in_bytes": 10324}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/ansi.py", "path_type": "hardlink", "sha256": "02fb352c76d275cc8ebc339da442d952850b7018987b063be9e341a7ab85061b", "sha256_in_prefix": "02fb352c76d275cc8ebc339da442d952850b7018987b063be9e341a7ab85061b", "size_in_bytes": 6921}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/bar.py", "path_type": "hardlink", "sha256": "95d6d51cecca24e9df95536ebf5c52ee0e9d2d7d84df03275e474f6e9cc94dcb", "sha256_in_prefix": "95d6d51cecca24e9df95536ebf5c52ee0e9d2d7d84df03275e474f6e9cc94dcb", "size_in_bytes": 3263}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/box.py", "path_type": "hardlink", "sha256": "9266af05cfdd9fbdcbfe0ffcbf1592c18243daecc15ce5054ed24e7e96dccdc3", "sha256_in_prefix": "9266af05cfdd9fbdcbfe0ffcbf1592c18243daecc15ce5054ed24e7e96dccdc3", "size_in_bytes": 10686}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/cells.py", "path_type": "hardlink", "sha256": "2ab4248f9f8b821082a492d23502320198e775ce1b9c4a8e1268b962e67d5026", "sha256_in_prefix": "2ab4248f9f8b821082a492d23502320198e775ce1b9c4a8e1268b962e67d5026", "size_in_bytes": 5130}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/color.py", "path_type": "hardlink", "sha256": "dc74942d50e3eea4245d47455afefc24e8926737f2e72d6791c6219dadbde95d", "sha256_in_prefix": "dc74942d50e3eea4245d47455afefc24e8926737f2e72d6791c6219dadbde95d", "size_in_bytes": 18211}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/color_triplet.py", "path_type": "hardlink", "sha256": "de585091d25bbd63e82c33be0276089805a626f579765818342559f7b39168de", "sha256_in_prefix": "de585091d25bbd63e82c33be0276089805a626f579765818342559f7b39168de", "size_in_bytes": 1054}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/columns.py", "path_type": "hardlink", "sha256": "1d45f429c326f5db0a362d757d36e233f876883b65f3248269573195a944ceaf", "sha256_in_prefix": "1d45f429c326f5db0a362d757d36e233f876883b65f3248269573195a944ceaf", "size_in_bytes": 7131}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/console.py", "path_type": "hardlink", "sha256": "b7d6b366999131553972985505949284db26071776f880167134e5868afe135b", "sha256_in_prefix": "b7d6b366999131553972985505949284db26071776f880167134e5868afe135b", "size_in_bytes": 100849}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/constrain.py", "path_type": "hardlink", "sha256": "d5520fb82f0082d296adc9dc42b8c1758a80dc9556cacbba8d9a35aeb87b73b4", "sha256_in_prefix": "d5520fb82f0082d296adc9dc42b8c1758a80dc9556cacbba8d9a35aeb87b73b4", "size_in_bytes": 1288}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/containers.py", "path_type": "hardlink", "sha256": "73fe7a4f171e74662a0dea4704c4ee65d5088a38ad010827a31f9075ed19d6aa", "sha256_in_prefix": "73fe7a4f171e74662a0dea4704c4ee65d5088a38ad010827a31f9075ed19d6aa", "size_in_bytes": 5502}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/control.py", "path_type": "hardlink", "sha256": "1144d25052d06f163a666a3fb1d33ee4bb37db7bc81d305f37c4cfba5a9e1d46", "sha256_in_prefix": "1144d25052d06f163a666a3fb1d33ee4bb37db7bc81d305f37c4cfba5a9e1d46", "size_in_bytes": 6487}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/default_styles.py", "path_type": "hardlink", "sha256": "921405aaa6a80ecddba6b32a5a91f0f273b95291b60cde90b6e4dde8bcd9c187", "sha256_in_prefix": "921405aaa6a80ecddba6b32a5a91f0f273b95291b60cde90b6e4dde8bcd9c187", "size_in_bytes": 8257}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/diagnose.py", "path_type": "hardlink", "sha256": "7c99754c8b519f5f601b0a2ea9383ef333d45b762a401a8696dadf3d0b351fdc", "sha256_in_prefix": "7c99754c8b519f5f601b0a2ea9383ef333d45b762a401a8696dadf3d0b351fdc", "size_in_bytes": 1025}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/emoji.py", "path_type": "hardlink", "sha256": "59de1b42e6d9752cbaf8fcab45036e307b67d959258caf6e3d93d596ed9c9b1d", "sha256_in_prefix": "59de1b42e6d9752cbaf8fcab45036e307b67d959258caf6e3d93d596ed9c9b1d", "size_in_bytes": 2367}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/errors.py", "path_type": "hardlink", "sha256": "e693f729ce5de1027f734285b31adfca18e23d57bb275ccea9215b140cdc57e6", "sha256_in_prefix": "e693f729ce5de1027f734285b31adfca18e23d57bb275ccea9215b140cdc57e6", "size_in_bytes": 642}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/file_proxy.py", "path_type": "hardlink", "sha256": "4e5f531cc0d9f8f9395a6f2c23580683f5390e1bac9b10fe159d1f51b714d16d", "sha256_in_prefix": "4e5f531cc0d9f8f9395a6f2c23580683f5390e1bac9b10fe159d1f51b714d16d", "size_in_bytes": 1683}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/filesize.py", "path_type": "hardlink", "sha256": "fe2cfd948a5182f5bb30d49e0999cb83e1f0cdb3f81844e0e78dd6a83f1216cd", "sha256_in_prefix": "fe2cfd948a5182f5bb30d49e0999cb83e1f0cdb3f81844e0e78dd6a83f1216cd", "size_in_bytes": 2484}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/highlighter.py", "path_type": "hardlink", "sha256": "1bfb27fbc0ca8ccd6c1232c6fe8738a2f9169a25295af8fc6d78b4c9e7762e76", "sha256_in_prefix": "1bfb27fbc0ca8ccd6c1232c6fe8738a2f9169a25295af8fc6d78b4c9e7762e76", "size_in_bytes": 9586}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/json.py", "path_type": "hardlink", "sha256": "bd512829d6b0a094630056b23f05e43013cbcbb4524ecf9fe38c124034769c9d", "sha256_in_prefix": "bd512829d6b0a094630056b23f05e43013cbcbb4524ecf9fe38c124034769c9d", "size_in_bytes": 5031}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/jupyter.py", "path_type": "hardlink", "sha256": "432a0aa04ffc21d09baed8921e9f53b1348dc931d8d053b9c2113b8ce4ddf541", "sha256_in_prefix": "432a0aa04ffc21d09baed8921e9f53b1348dc931d8d053b9c2113b8ce4ddf541", "size_in_bytes": 3252}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/layout.py", "path_type": "hardlink", "sha256": "6a3912140b4456ff44153705b3ec38b997dfb7b9c45e13732fb655760ad3e6b2", "sha256_in_prefix": "6a3912140b4456ff44153705b3ec38b997dfb7b9c45e13732fb655760ad3e6b2", "size_in_bytes": 14004}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/live.py", "path_type": "hardlink", "sha256": "b45dee90000967f37665b19c96a67e8b02e822867c7f41c7533efd8c0c89aa3f", "sha256_in_prefix": "b45dee90000967f37665b19c96a67e8b02e822867c7f41c7533efd8c0c89aa3f", "size_in_bytes": 15180}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/live_render.py", "path_type": "hardlink", "sha256": "22dff7f58773ac19bca372cbd24686a2b3c583e05f65601cac18cb8c5a246f1e", "sha256_in_prefix": "22dff7f58773ac19bca372cbd24686a2b3c583e05f65601cac18cb8c5a246f1e", "size_in_bytes": 3521}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/logging.py", "path_type": "hardlink", "sha256": "e4a68f3d230ff45c5c5cf05c28ce1c19dff35bbf0f3207fe61b3159ef7d2c34a", "sha256_in_prefix": "e4a68f3d230ff45c5c5cf05c28ce1c19dff35bbf0f3207fe61b3159ef7d2c34a", "size_in_bytes": 12468}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/markup.py", "path_type": "hardlink", "sha256": "ddeb8628fe6ce353424306928d39c9c6eb398993078f1a483345ba7c2c6b6b7f", "sha256_in_prefix": "ddeb8628fe6ce353424306928d39c9c6eb398993078f1a483345ba7c2c6b6b7f", "size_in_bytes": 8451}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/measure.py", "path_type": "hardlink", "sha256": "1e6ac8257f2c5914c76e087c33111acbff37564a8d5bfef4b3c68a3f965c608f", "sha256_in_prefix": "1e6ac8257f2c5914c76e087c33111acbff37564a8d5bfef4b3c68a3f965c608f", "size_in_bytes": 5305}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/padding.py", "path_type": "hardlink", "sha256": "295108ded3b0a3db202b560d4ae1fffccd7f8d45a62d9c11555fca98eb55cf23", "sha256_in_prefix": "295108ded3b0a3db202b560d4ae1fffccd7f8d45a62d9c11555fca98eb55cf23", "size_in_bytes": 4908}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/pager.py", "path_type": "hardlink", "sha256": "48efc44c114a6e0de7fc080ecd79b8d52bf7e98c57032237fd1f8a398dbfb927", "sha256_in_prefix": "48efc44c114a6e0de7fc080ecd79b8d52bf7e98c57032237fd1f8a398dbfb927", "size_in_bytes": 828}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/palette.py", "path_type": "hardlink", "sha256": "9489ef4753830d3d9fdd464c7cbd60aeaedd63fa4374a1f0e1b75480e19a3386", "sha256_in_prefix": "9489ef4753830d3d9fdd464c7cbd60aeaedd63fa4374a1f0e1b75480e19a3386", "size_in_bytes": 3396}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/panel.py", "path_type": "hardlink", "sha256": "f6c425d3484f22a1f91b68002d0a3835ea45c293f493dc13facfe03a6b39a487", "sha256_in_prefix": "f6c425d3484f22a1f91b68002d0a3835ea45c293f493dc13facfe03a6b39a487", "size_in_bytes": 11157}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/pretty.py", "path_type": "hardlink", "sha256": "832dd2ef6bb8151836cada28ecdd590d60c8bc1e2e9dbcdde625067609bef1f7", "sha256_in_prefix": "832dd2ef6bb8151836cada28ecdd590d60c8bc1e2e9dbcdde625067609bef1f7", "size_in_bytes": 36391}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/progress.py", "path_type": "hardlink", "sha256": "09473696453e5f9f6655d19f8cc0819197a218f2f7bb174e36384d245d93ef06", "sha256_in_prefix": "09473696453e5f9f6655d19f8cc0819197a218f2f7bb174e36384d245d93ef06", "size_in_bytes": 60408}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/progress_bar.py", "path_type": "hardlink", "sha256": "9994cfa4953071f71d8100934f3de4c98f9f73bf5d74bc2dc7a1a18717e8d3ae", "sha256_in_prefix": "9994cfa4953071f71d8100934f3de4c98f9f73bf5d74bc2dc7a1a18717e8d3ae", "size_in_bytes": 8162}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/prompt.py", "path_type": "hardlink", "sha256": "974461414fb45154d5f5ed3cc56d416c88f426ad885f20a15f8942d2514dcede", "sha256_in_prefix": "974461414fb45154d5f5ed3cc56d416c88f426ad885f20a15f8942d2514dcede", "size_in_bytes": 12447}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/protocol.py", "path_type": "hardlink", "sha256": "e611c70c3347724764f22587e7311b8becee215485e616d4da3228e3b47b9531", "sha256_in_prefix": "e611c70c3347724764f22587e7311b8becee215485e616d4da3228e3b47b9531", "size_in_bytes": 1391}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/region.py", "path_type": "hardlink", "sha256": "acd4fdc59ad56536085d90b43589f8d42250c1835b47e29e70f3b14e042f07c6", "sha256_in_prefix": "acd4fdc59ad56536085d90b43589f8d42250c1835b47e29e70f3b14e042f07c6", "size_in_bytes": 166}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/repr.py", "path_type": "hardlink", "sha256": "e4c64966638d802ea4b9df905befe6d68917c0bd9a47abbacbea54714089cf6f", "sha256_in_prefix": "e4c64966638d802ea4b9df905befe6d68917c0bd9a47abbacbea54714089cf6f", "size_in_bytes": 4431}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/rule.py", "path_type": "hardlink", "sha256": "d1f35a4bf68445add43117374f958ca4dfecba6b43c5f6a8af6cb7a1fd5fb419", "sha256_in_prefix": "d1f35a4bf68445add43117374f958ca4dfecba6b43c5f6a8af6cb7a1fd5fb419", "size_in_bytes": 4602}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/scope.py", "path_type": "hardlink", "sha256": "4cc514f2aa35eed872a9008faa30cb62983f514d64e6a55df96c2226f9c955ab", "sha256_in_prefix": "4cc514f2aa35eed872a9008faa30cb62983f514d64e6a55df96c2226f9c955ab", "size_in_bytes": 2843}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/screen.py", "path_type": "hardlink", "sha256": "628791784494871ef882ba9bd264926fd960861cac5a6147621b1b3154235cef", "sha256_in_prefix": "628791784494871ef882ba9bd264926fd960861cac5a6147621b1b3154235cef", "size_in_bytes": 1591}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/segment.py", "path_type": "hardlink", "sha256": "a2d9ca78a18457e591950568b1f2557850dc0f100a1e9bc9fe12f34aee65ba63", "sha256_in_prefix": "a2d9ca78a18457e591950568b1f2557850dc0f100a1e9bc9fe12f34aee65ba63", "size_in_bytes": 24743}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/spinner.py", "path_type": "hardlink", "sha256": "a27221a4a9658d11e9a5365ab313bc91782d632087524a5280a825449de8e758", "sha256_in_prefix": "a27221a4a9658d11e9a5365ab313bc91782d632087524a5280a825449de8e758", "size_in_bytes": 4214}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/status.py", "path_type": "hardlink", "sha256": "9243e987761e019068f97fb8c0fa7c813a99c94e3ae8d2f06410383d94d37b0a", "sha256_in_prefix": "9243e987761e019068f97fb8c0fa7c813a99c94e3ae8d2f06410383d94d37b0a", "size_in_bytes": 4424}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/style.py", "path_type": "hardlink", "sha256": "c698f8b8c05932db8db8da267d489a9a2825de9d6c0ef4c2670ac6d6d71355e8", "sha256_in_prefix": "c698f8b8c05932db8db8da267d489a9a2825de9d6c0ef4c2670ac6d6d71355e8", "size_in_bytes": 27059}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/styled.py", "path_type": "hardlink", "sha256": "799367cc6ac8e248bfe78a606373a3d13fb1de5c5d5d3621e3faf20c1db8c015", "sha256_in_prefix": "799367cc6ac8e248bfe78a606373a3d13fb1de5c5d5d3621e3faf20c1db8c015", "size_in_bytes": 1258}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/syntax.py", "path_type": "hardlink", "sha256": "78328847097ef9e6742f0a3675ad9146d7eeb5719abeb24eeb50a5e4e912e7d5", "sha256_in_prefix": "78328847097ef9e6742f0a3675ad9146d7eeb5719abeb24eeb50a5e4e912e7d5", "size_in_bytes": 36371}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/table.py", "path_type": "hardlink", "sha256": "6664fb57b30c08e60ac3b4c663d4992f26037fa25d33e5957f4ec5755b958532", "sha256_in_prefix": "6664fb57b30c08e60ac3b4c663d4992f26037fa25d33e5957f4ec5755b958532", "size_in_bytes": 40049}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/terminal_theme.py", "path_type": "hardlink", "sha256": "d63e7eb9f25f9ef940a3942c8bf0026625c39b0317cea826141c8e6d3f7ec896", "sha256_in_prefix": "d63e7eb9f25f9ef940a3942c8bf0026625c39b0317cea826141c8e6d3f7ec896", "size_in_bytes": 3370}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/text.py", "path_type": "hardlink", "sha256": "00eec93c2cfafa068dd6d8552d73019ed1260cf55816014d1b5a0ceb5fec6a75", "sha256_in_prefix": "00eec93c2cfafa068dd6d8552d73019ed1260cf55816014d1b5a0ceb5fec6a75", "size_in_bytes": 47552}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/theme.py", "path_type": "hardlink", "sha256": "a0dca15e119a82d0e56c3c9eded56eddeb16396934bcd92ec45c3efee9e568ad", "sha256_in_prefix": "a0dca15e119a82d0e56c3c9eded56eddeb16396934bcd92ec45c3efee9e568ad", "size_in_bytes": 3771}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/themes.py", "path_type": "hardlink", "sha256": "d318132e8cdf69b79b62d709b43742e50917e4855411abe2a83509261e185459", "sha256_in_prefix": "d318132e8cdf69b79b62d709b43742e50917e4855411abe2a83509261e185459", "size_in_bytes": 102}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/traceback.py", "path_type": "hardlink", "sha256": "7345a607f2f4e3f51f65b2daa07f7cdbf53fb3b7a8b3128c5220159833dd4585", "sha256_in_prefix": "7345a607f2f4e3f51f65b2daa07f7cdbf53fb3b7a8b3128c5220159833dd4585", "size_in_bytes": 35861}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/tree.py", "path_type": "hardlink", "sha256": "c969d0eab02f446277a991aa06bc52d925b64ca05336b3f449d63c4313853eec", "sha256_in_prefix": "c969d0eab02f446277a991aa06bc52d925b64ca05336b3f449d63c4313853eec", "size_in_bytes": 9451}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli/__init__.py", "path_type": "hardlink", "sha256": "3e1370fdec8b81d9fb31c27a9eb00df32226ddd5c2ef9bebd6c546555c034a90", "sha256_in_prefix": "3e1370fdec8b81d9fb31c27a9eb00df32226ddd5c2ef9bebd6c546555c034a90", "size_in_bytes": 314}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli/_parser.py", "path_type": "hardlink", "sha256": "f70f0b1b48c1edfc26659581d2f5576de7a30c7725e00348271076b1c1270e50", "sha256_in_prefix": "f70f0b1b48c1edfc26659581d2f5576de7a30c7725e00348271076b1c1270e50", "size_in_bytes": 25591}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli/_re.py", "path_type": "hardlink", "sha256": "b21e2c0434603bde0a259c0d22b81d73257fa906acb79d18bf3380506a510ca0", "sha256_in_prefix": "b21e2c0434603bde0a259c0d22b81d73257fa906acb79d18bf3380506a510ca0", "size_in_bytes": 3171}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli/_types.py", "path_type": "hardlink", "sha256": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "sha256_in_prefix": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "size_in_bytes": 254}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli_w/__init__.py", "path_type": "hardlink", "sha256": "d05f320ed5f1dd4ba7866f3be0aac070fefab2bb0cf7ccbb5b21d0c02ba565ba", "sha256_in_prefix": "d05f320ed5f1dd4ba7866f3be0aac070fefab2bb0cf7ccbb5b21d0c02ba565ba", "size_in_bytes": 169}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli_w/_writer.py", "path_type": "hardlink", "sha256": "76c89f152db161fd62efa9a64727f3f72d76e710bb67f1d0f38e592a126c617b", "sha256_in_prefix": "76c89f152db161fd62efa9a64727f3f72d76e710bb67f1d0f38e592a126c617b", "size_in_bytes": 6961}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli_w/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/__init__.py", "path_type": "hardlink", "sha256": "db04525628e34733cb5575335aabdd64b36c10e8437e8a4a2ddd8428060bc0a5", "sha256_in_prefix": "db04525628e34733cb5575335aabdd64b36c10e8437e8a4a2ddd8428060bc0a5", "size_in_bytes": 1320}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/_api.py", "path_type": "hardlink", "sha256": "69ff201191bfbe1b2e7626bdbf3e1eb37561f3102a873433e026e3b3afebc6eb", "sha256_in_prefix": "69ff201191bfbe1b2e7626bdbf3e1eb37561f3102a873433e026e3b3afebc6eb", "size_in_bytes": 11234}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/_macos.py", "path_type": "hardlink", "sha256": "9d994b90e9accd413483aaf2470055198e423b33f2b9d72c889b4359aacce4b4", "sha256_in_prefix": "9d994b90e9accd413483aaf2470055198e423b33f2b9d72c889b4359aacce4b4", "size_in_bytes": 20503}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/_openssl.py", "path_type": "hardlink", "sha256": "2cb519ed919a8a8fa2e5da4a2a328249e4ae7e69fa4fca62f650dc167bd2caad", "sha256_in_prefix": "2cb519ed919a8a8fa2e5da4a2a328249e4ae7e69fa4fca62f650dc167bd2caad", "size_in_bytes": 2324}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/_ssl_constants.py", "path_type": "hardlink", "sha256": "3540f87d529d483d36ae2efe75bd2d9ced15a8b3fd687bb3992b5c5bbb40974f", "sha256_in_prefix": "3540f87d529d483d36ae2efe75bd2d9ced15a8b3fd687bb3992b5c5bbb40974f", "size_in_bytes": 1130}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/_windows.py", "path_type": "hardlink", "sha256": "ac01f22980fc33bb7e6d77c6f1580e55add3a5f85585bb78ad94253b8e58b8ff", "sha256_in_prefix": "ac01f22980fc33bb7e6d77c6f1580e55add3a5f85585bb78ad94253b8e58b8ff", "size_in_bytes": 17993}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__init__.py", "path_type": "hardlink", "sha256": "8972dc6222724a7d0635b58e3990c30298012f52603f8e0467c8b5efad12f0c7", "sha256_in_prefix": "8972dc6222724a7d0635b58e3990c30298012f52603f8e0467c8b5efad12f0c7", "size_in_bytes": 3333}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/_collections.py", "path_type": "hardlink", "sha256": "a72012249856ef074ea6a263f50240f05c8645fafc13cb94521a94be1174ef6f", "sha256_in_prefix": "a72012249856ef074ea6a263f50240f05c8645fafc13cb94521a94be1174ef6f", "size_in_bytes": 11372}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/_version.py", "path_type": "hardlink", "sha256": "b7dc0607aa283935d782263ae8ad66e81652d422725c7014f04a160d37ba4a19", "sha256_in_prefix": "b7dc0607aa283935d782263ae8ad66e81652d422725c7014f04a160d37ba4a19", "size_in_bytes": 64}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/connection.py", "path_type": "hardlink", "sha256": "b6d200f74f41adb4d4cf092a11efd3cd9561e0938e8fb83ad58b1e8b69abc068", "sha256_in_prefix": "b6d200f74f41adb4d4cf092a11efd3cd9561e0938e8fb83ad58b1e8b69abc068", "size_in_bytes": 20314}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/connectionpool.py", "path_type": "hardlink", "sha256": "7b67a203035b14d08ac63e1bc0328d2bec3b1c8752cf73a633153f4c8b7e7af4", "sha256_in_prefix": "7b67a203035b14d08ac63e1bc0328d2bec3b1c8752cf73a633153f4c8b7e7af4", "size_in_bytes": 40408}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_appengine_environ.py", "path_type": "hardlink", "sha256": "6c36f2384856d8228b25c42a00a032ac41cdf9a925b321c52aaeaf17c645b269", "sha256_in_prefix": "6c36f2384856d8228b25c42a00a032ac41cdf9a925b321c52aaeaf17c645b269", "size_in_bytes": 957}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/bindings.py", "path_type": "hardlink", "sha256": "e1793ae2a2243c1b74f40e6af9120552e0e135cf665e29556a99bb5a7627cd1c", "sha256_in_prefix": "e1793ae2a2243c1b74f40e6af9120552e0e135cf665e29556a99bb5a7627cd1c", "size_in_bytes": 17632}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/low_level.py", "path_type": "hardlink", "sha256": "076241076fcd44fd36c4ae8309ad4f6bd22ec6b3f0c730f365b8b14246fb53d3", "sha256_in_prefix": "076241076fcd44fd36c4ae8309ad4f6bd22ec6b3f0c730f365b8b14246fb53d3", "size_in_bytes": 13922}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/appengine.py", "path_type": "hardlink", "sha256": "551ebc780544d77ee5c53823043c029dae5488165338a6b4d408fffb905a0b3e", "sha256_in_prefix": "551ebc780544d77ee5c53823043c029dae5488165338a6b4d408fffb905a0b3e", "size_in_bytes": 11036}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/ntlmpool.py", "path_type": "hardlink", "sha256": "3657e45bb58c756f338aab9da298c7a16dbdf688350535a2d0878889baae1709", "sha256_in_prefix": "3657e45bb58c756f338aab9da298c7a16dbdf688350535a2d0878889baae1709", "size_in_bytes": 4528}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/pyopenssl.py", "path_type": "hardlink", "sha256": "843261e0c87263fa7ea0a9457187106954110efe86326046b96f728f1c9e7a33", "sha256_in_prefix": "843261e0c87263fa7ea0a9457187106954110efe86326046b96f728f1c9e7a33", "size_in_bytes": 17081}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/securetransport.py", "path_type": "hardlink", "sha256": "15e7f5208514147aa97afcd78833db20690329c858d8554a79578b191d50ab78", "sha256_in_prefix": "15e7f5208514147aa97afcd78833db20690329c858d8554a79578b191d50ab78", "size_in_bytes": 34446}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/socks.py", "path_type": "hardlink", "sha256": "6918bd7965e8f5911bf795d4c5e7f8676d421659e78db122028f473ac7a832de", "sha256_in_prefix": "6918bd7965e8f5911bf795d4c5e7f8676d421659e78db122028f473ac7a832de", "size_in_bytes": 7097}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/exceptions.py", "path_type": "hardlink", "sha256": "d0c9e7a372874cd7d745f63beb7f0db9f38f9146fa9973a6f8baa3fb8c76c3c0", "sha256_in_prefix": "d0c9e7a372874cd7d745f63beb7f0db9f38f9146fa9973a6f8baa3fb8c76c3c0", "size_in_bytes": 8217}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/fields.py", "path_type": "hardlink", "sha256": "92f2c30a0fc9987d652e3514118fc52d2f14858ee106f0cfb951136d8f2676b3", "sha256_in_prefix": "92f2c30a0fc9987d652e3514118fc52d2f14858ee106f0cfb951136d8f2676b3", "size_in_bytes": 8579}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/filepost.py", "path_type": "hardlink", "sha256": "e5bfeaaa04475652fbb8bb5d018073061f861e653901f255b7fd8dd174b73de6", "sha256_in_prefix": "e5bfeaaa04475652fbb8bb5d018073061f861e653901f255b7fd8dd174b73de6", "size_in_bytes": 2440}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/makefile.py", "path_type": "hardlink", "sha256": "9dbcedde2d1a80f54fd3b8eaaa08e16988cc9ae022fd6e44d04cb0662bd53bc1", "sha256_in_prefix": "9dbcedde2d1a80f54fd3b8eaaa08e16988cc9ae022fd6e44d04cb0662bd53bc1", "size_in_bytes": 1417}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/weakref_finalize.py", "path_type": "hardlink", "sha256": "b5109a97938084d491c9bd03847a7edfc02d2250ac44ff01c45dcd5feeaba880", "sha256_in_prefix": "b5109a97938084d491c9bd03847a7edfc02d2250ac44ff01c45dcd5feeaba880", "size_in_bytes": 5343}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/six.py", "path_type": "hardlink", "sha256": "6fd2ccd30057bfb13b4ab6c28c09b8c3037e86b1fe88dc6fd7c2e058d30c28fa", "sha256_in_prefix": "6fd2ccd30057bfb13b4ab6c28c09b8c3037e86b1fe88dc6fd7c2e058d30c28fa", "size_in_bytes": 34665}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/poolmanager.py", "path_type": "hardlink", "sha256": "696ca15d1b4d3b82549c249556a29329077c1174ef526d5537da60b366dc38da", "sha256_in_prefix": "696ca15d1b4d3b82549c249556a29329077c1174ef526d5537da60b366dc38da", "size_in_bytes": 19990}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/request.py", "path_type": "hardlink", "sha256": "61358536bed023087b1355bd75d7bd2ccefbbf65564c9e55efc5ee4d3c3b0f50", "sha256_in_prefix": "61358536bed023087b1355bd75d7bd2ccefbbf65564c9e55efc5ee4d3c3b0f50", "size_in_bytes": 6691}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/response.py", "path_type": "hardlink", "sha256": "7e60c9005906ef5b854e7fac5524e1d88c345a6717418aa46d18e286fc018d4f", "sha256_in_prefix": "7e60c9005906ef5b854e7fac5524e1d88c345a6717418aa46d18e286fc018d4f", "size_in_bytes": 30641}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__init__.py", "path_type": "hardlink", "sha256": "2449929a6aaa2f26b0f0fe75814226661f06c20f62d7349ef83a2a022b67da77", "sha256_in_prefix": "2449929a6aaa2f26b0f0fe75814226661f06c20f62d7349ef83a2a022b67da77", "size_in_bytes": 1155}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/connection.py", "path_type": "hardlink", "sha256": "e4bc760753d6dbd2b1067d93d3190dd420604416b780654904aa10a11a201159", "sha256_in_prefix": "e4bc760753d6dbd2b1067d93d3190dd420604416b780654904aa10a11a201159", "size_in_bytes": 4901}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/proxy.py", "path_type": "hardlink", "sha256": "cd4bcf3c226ba7a74e17437818055b39c97aa3ee2e5ca4ab1a24e492be6f512e", "sha256_in_prefix": "cd4bcf3c226ba7a74e17437818055b39c97aa3ee2e5ca4ab1a24e492be6f512e", "size_in_bytes": 1605}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/queue.py", "path_type": "hardlink", "sha256": "9d1817f3f797fbf564bf1a17d3de905a8cfc3ecd101d4004c482c263fecf9dc3", "sha256_in_prefix": "9d1817f3f797fbf564bf1a17d3de905a8cfc3ecd101d4004c482c263fecf9dc3", "size_in_bytes": 498}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/request.py", "path_type": "hardlink", "sha256": "0b4394b76b5c53a2d189027b61834ff46bcfad2be5ef388805e910fb99e50599", "sha256_in_prefix": "0b4394b76b5c53a2d189027b61834ff46bcfad2be5ef388805e910fb99e50599", "size_in_bytes": 3997}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/response.py", "path_type": "hardlink", "sha256": "189a60dc4822f6a6895d1c01879c2ff8c36e4566a7e4122ee34a117a8c563f6f", "sha256_in_prefix": "189a60dc4822f6a6895d1c01879c2ff8c36e4566a7e4122ee34a117a8c563f6f", "size_in_bytes": 3510}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/retry.py", "path_type": "hardlink", "sha256": "e8436f399f0f043ce1f24822c69aa5f6522b6f67711fe93b66605a9c9176360e", "sha256_in_prefix": "e8436f399f0f043ce1f24822c69aa5f6522b6f67711fe93b66605a9c9176360e", "size_in_bytes": 22050}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/ssl_.py", "path_type": "hardlink", "sha256": "403bae4f13d20a3d6b62d678c690fb531fabdb44c3e74687caa2b2850ec1ab80", "sha256_in_prefix": "403bae4f13d20a3d6b62d678c690fb531fabdb44c3e74687caa2b2850ec1ab80", "size_in_bytes": 17460}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/ssl_match_hostname.py", "path_type": "hardlink", "sha256": "22be1c65512398093c8140081d64a2ef0b4e3bcdd4098001636c450f5425fd60", "sha256_in_prefix": "22be1c65512398093c8140081d64a2ef0b4e3bcdd4098001636c450f5425fd60", "size_in_bytes": 5758}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/ssltransport.py", "path_type": "hardlink", "sha256": "340faee6b313ac3143142f10cd129410a306d39eb584e0f8a814ebdd9e29bfa1", "sha256_in_prefix": "340faee6b313ac3143142f10cd129410a306d39eb584e0f8a814ebdd9e29bfa1", "size_in_bytes": 6895}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/timeout.py", "path_type": "hardlink", "sha256": "730ab874c93cee624748192d2b59a2609fbce46fb74f74664f6d2fed2142a67a", "sha256_in_prefix": "730ab874c93cee624748192d2b59a2609fbce46fb74f74664f6d2fed2142a67a", "size_in_bytes": 10168}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/url.py", "path_type": "hardlink", "sha256": "942004ecce66c80f040dd5b4b09bb2c9985507d2bf8f7f258d684702715a5a81", "sha256_in_prefix": "942004ecce66c80f040dd5b4b09bb2c9985507d2bf8f7f258d684702715a5a81", "size_in_bytes": 14296}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/wait.py", "path_type": "hardlink", "sha256": "7ce5f4fdf6a8cc6d8fee25688d0a04d666f277078dc93726fa15c47c5ad3b4b2", "sha256_in_prefix": "7ce5f4fdf6a8cc6d8fee25688d0a04d666f277078dc93726fa15c47c5ad3b4b2", "size_in_bytes": 5403}, {"_path": "lib/python3.10/site-packages/pip/_vendor/vendor.txt", "path_type": "hardlink", "sha256": "7dac2af13d5715f061b38ae38d29787d40373f1f4fda6b46d9ebeaa8fca16e17", "sha256_in_prefix": "7dac2af13d5715f061b38ae38d29787d40373f1f4fda6b46d9ebeaa8fca16e17", "size_in_bytes": 343}, {"_path": "lib/python3.10/site-packages/pip/py.typed", "path_type": "hardlink", "sha256": "10156fbcf4539ff788a73e5ee50ced48276b317ed0c1ded53fddd14a82256762", "sha256_in_prefix": "10156fbcf4539ff788a73e5ee50ced48276b317ed0c1ded53fddd14a82256762", "size_in_bytes": 286}, {"_path": "lib/python3.10/site-packages/pip/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/__pycache__/__pip-runner__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/__pycache__/build_env.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/__pycache__/cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/autocompletion.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/base_command.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/cmdoptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/command_context.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/index_command.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/main.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/main_parser.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/parser.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/progress_bars.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/req_command.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/spinners.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/cli/__pycache__/status_codes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/check.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/completion.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/configuration.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/debug.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/download.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/freeze.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/hash.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/help.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/index.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/inspect.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/install.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/list.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/lock.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/search.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/show.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/uninstall.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/commands/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/__pycache__/configuration.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/distributions/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/distributions/__pycache__/base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/distributions/__pycache__/installed.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/distributions/__pycache__/sdist.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/distributions/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/index/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/index/__pycache__/collector.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/index/__pycache__/package_finder.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/index/__pycache__/sources.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/locations/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/locations/__pycache__/_distutils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/locations/__pycache__/_sysconfig.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/locations/__pycache__/base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/__pycache__/main.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/__pycache__/_json.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/__pycache__/base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/importlib/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/importlib/__pycache__/_compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/importlib/__pycache__/_dists.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/importlib/__pycache__/_envs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/metadata/__pycache__/pkg_resources.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/candidate.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/direct_url.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/format_control.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/index.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/installation_report.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/link.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/pylock.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/scheme.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/search_scope.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/selection_prefs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/target_python.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/models/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/__pycache__/auth.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/__pycache__/cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/__pycache__/download.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/__pycache__/lazy_wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/__pycache__/session.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/__pycache__/utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/network/__pycache__/xmlrpc.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/build_tracker.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/metadata.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/metadata_editable.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/metadata_legacy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/wheel_editable.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/build/__pycache__/wheel_legacy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/__pycache__/check.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/__pycache__/freeze.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/install/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/install/__pycache__/editable_legacy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/install/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/operations/__pycache__/prepare.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/__pycache__/pyproject.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/__pycache__/constructors.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/__pycache__/req_dependency_group.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/__pycache__/req_file.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/__pycache__/req_install.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/__pycache__/req_set.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/req/__pycache__/req_uninstall.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/__pycache__/base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/legacy/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/legacy/__pycache__/resolver.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/candidates.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/factory.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/found_candidates.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/provider.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/reporter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/requirements.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/resolution/resolvelib/__pycache__/resolver.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/__pycache__/self_outdated_check.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/_jaraco_text.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/_log.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/appdirs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/compatibility_tags.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/datetime.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/deprecation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/direct_url_helpers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/egg_link.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/entrypoints.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/filesystem.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/filetypes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/glibc.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/hashes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/logging.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/misc.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/packaging.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/retry.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/setuptools_build.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/subprocess.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/temp_dir.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/unpacking.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/urls.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/virtualenv.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/utils/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/bazaar.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/git.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/mercurial.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/subversion.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/vcs/__pycache__/versioncontrol.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_internal/__pycache__/wheel_builder.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/_cmd.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/adapter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/file_cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/redis_cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/controller.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/filewrapper.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/heuristics.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/serialize.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/cachecontrol/__pycache__/wrapper.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/certifi/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/certifi/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/certifi/__pycache__/core.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/_implementation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/_lint_dependency_groups.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/_pip_wrapper.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/dependency_groups/__pycache__/_toml_compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/__pycache__/compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/__pycache__/resources.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/__pycache__/scripts.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distlib/__pycache__/util.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distro/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distro/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/distro/__pycache__/distro.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/codec.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/core.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/idnadata.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/intranges.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/package_data.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/idna/__pycache__/uts46data.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/msgpack/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/msgpack/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/msgpack/__pycache__/ext.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/msgpack/__pycache__/fallback.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_elffile.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_manylinux.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_musllinux.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_parser.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_structures.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/_tokenizer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/licenses/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/licenses/__pycache__/_spdx.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/markers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/metadata.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/requirements.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/specifiers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/tags.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/packaging/__pycache__/version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pkg_resources/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/android.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/macos.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/unix.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/platformdirs/__pycache__/windows.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/console.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/filter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/filters/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/formatter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/formatters/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/formatters/__pycache__/_mapping.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/lexer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/__pycache__/_mapping.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/lexers/__pycache__/python.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/modeline.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/plugin.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/regexopt.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/scanner.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/sphinxext.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/style.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/styles/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/styles/__pycache__/_mapping.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/token.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/unistring.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pygments/__pycache__/util.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/__pycache__/_impl.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/_in_process/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/pyproject_hooks/_in_process/__pycache__/_in_process.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/__version__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/_internal_utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/adapters.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/auth.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/certs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/cookies.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/help.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/hooks.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/models.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/packages.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/sessions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/status_codes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/structures.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/requests/__pycache__/utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/__pycache__/providers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/__pycache__/reporters.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/abstract.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/criterion.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/resolution.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/resolvelib/__pycache__/structs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_cell_widths.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_emoji_codes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_emoji_replace.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_export_format.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_extension.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_fileno.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_inspect.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_log_render.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_loop.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_null_file.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_palettes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_pick.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_ratio.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_spinners.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_stack.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_timer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_win32_console.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_windows.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_windows_renderer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/_wrap.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/abc.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/align.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/ansi.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/bar.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/box.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/cells.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/color.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/color_triplet.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/columns.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/console.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/constrain.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/containers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/control.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/default_styles.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/diagnose.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/emoji.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/errors.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/file_proxy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/filesize.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/highlighter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/json.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/jupyter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/layout.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/live.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/live_render.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/logging.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/markup.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/measure.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/padding.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/pager.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/palette.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/panel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/pretty.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/progress.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/progress_bar.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/prompt.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/protocol.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/region.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/repr.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/rule.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/scope.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/screen.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/segment.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/spinner.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/status.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/style.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/styled.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/syntax.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/table.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/terminal_theme.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/text.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/theme.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/themes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/traceback.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/rich/__pycache__/tree.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli/__pycache__/_parser.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli/__pycache__/_re.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli/__pycache__/_types.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli_w/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/tomli_w/__pycache__/_writer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/_api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/_macos.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/_openssl.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/_ssl_constants.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/truststore/__pycache__/_windows.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/_collections.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/_version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/connection.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/connectionpool.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/_appengine_environ.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/bindings.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/low_level.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/appengine.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/ntlmpool.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/pyopenssl.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/securetransport.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/contrib/__pycache__/socks.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/fields.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/filepost.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/makefile.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/weakref_finalize.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/packages/__pycache__/six.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/poolmanager.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/request.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/__pycache__/response.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/connection.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/proxy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/queue.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/request.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/response.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/retry.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/ssl_.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/ssl_match_hostname.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/ssltransport.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/timeout.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/url.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.10/site-packages/pip/_vendor/urllib3/util/__pycache__/wait.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "bin/pip", "path_type": "unix_python_entry_point"}, {"_path": "bin/pip3", "path_type": "unix_python_entry_point"}], "paths_version": 1}, "platform": null, "requested_spec": "", "sha256": "ec9ed3cef137679f3e3a68e286c6efd52144684e1be0b05004d9699882dadcdd", "size": 1177168, "subdir": "noarch", "timestamp": 1753924973872, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/noarch/pip-25.2-pyh8b19718_0.conda", "version": "25.2"}
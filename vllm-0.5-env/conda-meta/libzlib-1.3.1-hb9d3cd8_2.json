{"arch": "x86_64", "build": "hb9d3cd8_2", "build_number": 2, "build_string": "hb9d3cd8_2", "channel": "conda-forge", "constrains": ["zlib 1.3.1 *_2"], "depends": ["__glibc >=2.17,<3.0.a0", "libgcc >=13"], "extracted_package_dir": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libzlib-1.3.1-hb9d3cd8_2", "files": ["lib/libz.so.1", "lib/libz.so.1.3.1"], "fn": "libzlib-1.3.1-hb9d3cd8_2.conda", "license": "<PERSON><PERSON><PERSON>", "license_family": "Other", "link": {"source": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libzlib-1.3.1-hb9d3cd8_2", "type": 1}, "md5": "edb0dca6bc32e4f4789199455a1dbeb8", "name": "libzlib", "package_tarball_full_path": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libzlib-1.3.1-hb9d3cd8_2.tar.bz2", "paths_data": {"paths": [{"_path": "lib/libz.so.1", "path_type": "softlink", "sha256_in_prefix": "b2e4560f2fc0518922113945050aa0ab6f79a8ccdab18dd1b173f68d0cbe78f9", "size_in_bytes": 112680}, {"_path": "lib/libz.so.1.3.1", "path_type": "hardlink", "sha256": "b2e4560f2fc0518922113945050aa0ab6f79a8ccdab18dd1b173f68d0cbe78f9", "sha256_in_prefix": "b2e4560f2fc0518922113945050aa0ab6f79a8ccdab18dd1b173f68d0cbe78f9", "size_in_bytes": 112680}], "paths_version": 1}, "platform": "linux", "requested_spec": "", "sha256": "d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4", "size": 60963, "subdir": "linux-64", "timestamp": 1727963148474, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda", "version": "1.3.1"}
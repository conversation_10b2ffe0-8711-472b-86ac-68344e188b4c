{"arch": null, "build": "hbd8a1cb_0", "build_number": 0, "build_string": "hbd8a1cb_0", "channel": "conda-forge", "constrains": [], "depends": ["__unix"], "extracted_package_dir": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/ca-certificates-2025.8.3-hbd8a1cb_0", "files": ["ssl/cacert.pem", "ssl/cert.pem"], "fn": "ca-certificates-2025.8.3-hbd8a1cb_0.conda", "license": "ISC", "link": {"source": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/ca-certificates-2025.8.3-hbd8a1cb_0", "type": 1}, "md5": "74784ee3d225fc3dca89edb635b4e5cc", "name": "ca-certificates", "noarch": "generic", "package_tarball_full_path": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/ca-certificates-2025.8.3-hbd8a1cb_0.tar.bz2", "paths_data": {"paths": [{"_path": "ssl/cacert.pem", "path_type": "hardlink", "sha256": "9102e6a3644a071ba6cdbd4a53698f291c4a64b18450a08bc046548b6db5cc8b", "sha256_in_prefix": "9102e6a3644a071ba6cdbd4a53698f291c4a64b18450a08bc046548b6db5cc8b", "size_in_bytes": 287634}, {"_path": "ssl/cert.pem", "path_type": "softlink", "sha256_in_prefix": "9102e6a3644a071ba6cdbd4a53698f291c4a64b18450a08bc046548b6db5cc8b", "size_in_bytes": 287634}], "paths_version": 1}, "platform": null, "requested_spec": "", "sha256": "837b795a2bb39b75694ba910c13c15fa4998d4bb2a622c214a6a5174b2ae53d1", "size": 154402, "subdir": "noarch", "timestamp": 1754210968730, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.8.3-hbd8a1cb_0.conda", "version": "2025.8.3"}
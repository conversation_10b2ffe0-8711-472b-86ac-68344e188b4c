{"arch": "x86_64", "build": "hb9d3cd8_2", "build_number": 2, "build_string": "hb9d3cd8_2", "channel": "conda-forge", "constrains": ["xz 5.8.1.*"], "depends": ["__glibc >=2.17,<3.0.a0", "libgcc >=13"], "extracted_package_dir": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/liblzma-5.8.1-hb9d3cd8_2", "files": ["lib/liblzma.so.5", "lib/liblzma.so.5.8.1"], "fn": "liblzma-5.8.1-hb9d3cd8_2.conda", "license": "0BSD", "link": {"source": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/liblzma-5.8.1-hb9d3cd8_2", "type": 1}, "md5": "1a580f7796c7bf6393fddb8bbbde58dc", "name": "liblzma", "package_tarball_full_path": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/liblzma-5.8.1-hb9d3cd8_2.tar.bz2", "paths_data": {"paths": [{"_path": "lib/liblzma.so.5", "path_type": "hardlink", "sha256": "99dbefdd5ce54e28207037149f3a249960149e39a56572d53be4e62234bdf5ad", "sha256_in_prefix": "99dbefdd5ce54e28207037149f3a249960149e39a56572d53be4e62234bdf5ad", "size_in_bytes": 222712}, {"_path": "lib/liblzma.so.5.8.1", "path_type": "hardlink", "sha256": "99dbefdd5ce54e28207037149f3a249960149e39a56572d53be4e62234bdf5ad", "sha256_in_prefix": "99dbefdd5ce54e28207037149f3a249960149e39a56572d53be4e62234bdf5ad", "size_in_bytes": 222712}], "paths_version": 1}, "platform": "linux", "requested_spec": "", "sha256": "f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8", "size": 112894, "subdir": "linux-64", "timestamp": 1749230047870, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda", "version": "5.8.1"}
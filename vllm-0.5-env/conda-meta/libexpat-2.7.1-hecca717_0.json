{"arch": "x86_64", "build": "hecca717_0", "build_number": 0, "build_string": "hecca717_0", "channel": "conda-forge", "constrains": ["expat 2.7.1.*"], "depends": ["__glibc >=2.17,<3.0.a0", "libgcc >=14"], "extracted_package_dir": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libexpat-2.7.1-hecca717_0", "files": ["lib/libexpat.so.1", "lib/libexpat.so.1.10.2"], "fn": "libexpat-2.7.1-hecca717_0.conda", "license": "MIT", "license_family": "MIT", "link": {"source": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libexpat-2.7.1-hecca717_0", "type": 1}, "md5": "4211416ecba1866fab0c6470986c22d6", "name": "libexpat", "package_tarball_full_path": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libexpat-2.7.1-hecca717_0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/libexpat.so.1", "path_type": "softlink", "sha256_in_prefix": "f26f63af3ee05da286368dc4cc0af0bbc829ec3d7c2943b5d9bb7766cd6a1caf", "size_in_bytes": 190928}, {"_path": "lib/libexpat.so.1.10.2", "path_type": "hardlink", "sha256": "f26f63af3ee05da286368dc4cc0af0bbc829ec3d7c2943b5d9bb7766cd6a1caf", "sha256_in_prefix": "f26f63af3ee05da286368dc4cc0af0bbc829ec3d7c2943b5d9bb7766cd6a1caf", "size_in_bytes": 190928}], "paths_version": 1}, "platform": "linux", "requested_spec": "", "sha256": "da2080da8f0288b95dd86765c801c6e166c4619b910b11f9a8446fb852438dc2", "size": 74811, "subdir": "linux-64", "timestamp": 1752719572741, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda", "version": "2.7.1"}
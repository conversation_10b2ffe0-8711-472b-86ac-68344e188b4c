{"arch": "x86_64", "build": "h0c1763c_0", "build_number": 0, "build_string": "h0c1763c_0", "channel": "conda-forge", "constrains": [], "depends": ["__glibc >=2.17,<3.0.a0", "libgcc >=14", "libzlib >=1.3.1,<2.0a0"], "extracted_package_dir": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libsqlite-3.50.4-h0c1763c_0", "files": ["include/sqlite3.h", "include/sqlite3ext.h", "lib/libsqlite3.so", "lib/libsqlite3.so.0", "lib/libsqlite3.so.3.50.4", "lib/pkgconfig/sqlite3.pc"], "fn": "libsqlite-3.50.4-h0c1763c_0.conda", "license": "blessing", "link": {"source": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libsqlite-3.50.4-h0c1763c_0", "type": 1}, "md5": "0b367fad34931cb79e0d6b7e5c06bb1c", "name": "libsqlite", "package_tarball_full_path": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libsqlite-3.50.4-h0c1763c_0.tar.bz2", "paths_data": {"paths": [{"_path": "include/sqlite3.h", "path_type": "hardlink", "sha256": "abd1514e0351f79393d1be882830afdb40a8099e8257f311f0bfdf8486f11bea", "sha256_in_prefix": "abd1514e0351f79393d1be882830afdb40a8099e8257f311f0bfdf8486f11bea", "size_in_bytes": 661968}, {"_path": "include/sqlite3ext.h", "path_type": "hardlink", "sha256": "9a91de0d5e5ccc04ec59041275c67972d6f8894f7543a10033e387b69987beb5", "sha256_in_prefix": "9a91de0d5e5ccc04ec59041275c67972d6f8894f7543a10033e387b69987beb5", "size_in_bytes": 38321}, {"_path": "lib/libsqlite3.so", "path_type": "softlink", "sha256_in_prefix": "df054c7d315d91a59d5be2897a77f393aff9e8cb7c972b53099fe1ad8d0ecfc9", "size_in_bytes": 1713872}, {"_path": "lib/libsqlite3.so.0", "path_type": "softlink", "sha256_in_prefix": "df054c7d315d91a59d5be2897a77f393aff9e8cb7c972b53099fe1ad8d0ecfc9", "size_in_bytes": 1713872}, {"_path": "lib/libsqlite3.so.3.50.4", "path_type": "hardlink", "sha256": "df054c7d315d91a59d5be2897a77f393aff9e8cb7c972b53099fe1ad8d0ecfc9", "sha256_in_prefix": "df054c7d315d91a59d5be2897a77f393aff9e8cb7c972b53099fe1ad8d0ecfc9", "size_in_bytes": 1713872}, {"_path": "lib/pkgconfig/sqlite3.pc", "path_type": "hardlink", "sha256": "3f0ec96419cb8c5b89d4a7b6a7529dab6d8fab1bd1dc0d5dd5d9400aed14ab91", "sha256_in_prefix": "ca13d402eed0cc5c695989f0a57970512e919cee87b047571b187cfc09cfc02e", "size_in_bytes": 531}], "paths_version": 1}, "platform": "linux", "requested_spec": "", "sha256": "6d9c32fc369af5a84875725f7ddfbfc2ace795c28f246dc70055a79f9b2003da", "size": 932581, "subdir": "linux-64", "timestamp": 1753948484112, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.4-h0c1763c_0.conda", "version": "3.50.4"}
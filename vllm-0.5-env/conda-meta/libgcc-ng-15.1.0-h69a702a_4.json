{"arch": "x86_64", "build": "h69a702a_4", "build_number": 4, "build_string": "h69a702a_4", "channel": "conda-forge", "constrains": [], "depends": ["libgcc 15.1.0 h767d61c_4"], "extracted_package_dir": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libgcc-ng-15.1.0-h69a702a_4", "files": [], "fn": "libgcc-ng-15.1.0-h69a702a_4.conda", "license": "GPL-3.0-only WITH GCC-exception-3.1", "link": {"source": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libgcc-ng-15.1.0-h69a702a_4", "type": 1}, "md5": "28771437ffcd9f3417c66012dc49a3be", "name": "libgcc-ng", "package_tarball_full_path": "/share_data/users/yukaifeng/.local/share/mamba/pkgs/libgcc-ng-15.1.0-h69a702a_4.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "platform": "linux", "requested_spec": "", "sha256": "76ceac93ed98f208363d6e9c75011b0ff7b97b20f003f06461a619557e726637", "size": 29249, "subdir": "linux-64", "timestamp": 1753903872571, "track_features": "", "url": "https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_4.conda", "version": "15.1.0"}